<!doctype html>
<html lang="zh" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Notes" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Maximizing Sticky Notes | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Notes"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Maximizing Sticky Notes | FunBlocks AI"><meta data-rh="true" name="description" content="Sticky Notes are one of the most versatile node types in the FunBlocks AIFlow environment. This guide will help you understand how to leverage them effectively in your workflows."><meta data-rh="true" property="og:description" content="Sticky Notes are one of the most versatile node types in the FunBlocks AIFlow environment. This guide will help you understand how to leverage them effectively in your workflows."><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Notes"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Notes" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Notes" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Notes" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.2b6e2e3c.css">
<script src="/zh/assets/js/runtime~main.01201d88.js" defer="defer"></script>
<script src="/zh/assets/js/main.36e42d00.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/aiflow_sticky_notes_ai_tools.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/aiflow_sticky_notes_critical_thinking_tools.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Notes" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Notes" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="回到顶部" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="文档侧边栏" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/zh/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="展开侧边栏分类 &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/zh/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="折叠侧边栏分类 &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/ai-tools">AI Tools</a><button aria-label="展开侧边栏分类 &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="页面路径"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="主页面" class="breadcrumbs__link" href="/zh/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/zh/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Maximizing Sticky Notes</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">本页总览</button></div><div class="theme-doc-markdown markdown"><header><h1>Maximizing Sticky Notes</h1></header>
<p>Sticky Notes are one of the most versatile node types in the FunBlocks AIFlow environment. This guide will help you understand how to leverage them effectively in your workflows.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="what-are-sticky-notes">What Are Sticky Notes?<a href="#what-are-sticky-notes" class="hash-link" aria-label="What Are Sticky Notes?的直接链接" title="What Are Sticky Notes?的直接链接">​</a></h2>
<p>Sticky Notes in AIFlow function as flexible text containers that bridge human editing and AI processing. They serve as:</p>
<ul>
<li>Editable clones of AI-generated content</li>
<li>Quick capture tools for your thoughts and ideas</li>
<li>Input containers for text from external sources</li>
</ul>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow: copy node to Sticky Notes" src="/zh/assets/images/aiflow_copy_to_sticky_notes-123517bd53828fb1237063e3155f22d7.png" width="2188" height="1622" class="img_ev3q"></p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="getting-started-with-sticky-notes">Getting Started with Sticky Notes<a href="#getting-started-with-sticky-notes" class="hash-link" aria-label="Getting Started with Sticky Notes的直接链接" title="Getting Started with Sticky Notes的直接链接">​</a></h2>
<p>You can easily add a Sticky Note to your workflow by selecting it from the tools panel on the left side of the interface. This allows you to capture ideas immediately without interrupting your creative flow.</p>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow Panel tool: Sticky Notes" src="/zh/assets/images/aiflow_panel_notes-42294eb0e43cb898a115fb9e099b2756.png" width="1210" height="648" class="img_ev3q"></p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="key-features-and-use-cases">Key Features and Use Cases<a href="#key-features-and-use-cases" class="hash-link" aria-label="Key Features and Use Cases的直接链接" title="Key Features and Use Cases的直接链接">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="content-processing">Content Processing<a href="#content-processing" class="hash-link" aria-label="Content Processing的直接链接" title="Content Processing的直接链接">​</a></h3>
<p>Like other nodes in AIFlow, Sticky Notes have access to the AI functionality menu. This means you can use the content within your note as input for various AI operations:</p>
<ul>
<li>Generate presentation slides from your notes</li>
<li>Have the AI review and provide feedback on written content</li>
<li>Transform rough ideas into polished articles</li>
</ul>
<img src="/img/portfolio/fullsize/aiflow_sticky_notes_ai_tools.png" width="500" alt="FunBlocks AIFlow Sticky Notes: convert to articles">
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="importing-external-content">Importing External Content<a href="#importing-external-content" class="hash-link" aria-label="Importing External Content的直接链接" title="Importing External Content的直接链接">​</a></h3>
<p>When you have large blocks of text from other sources that you want to process with AI, Sticky Notes provide an ideal landing spot. Simply paste your content into a Sticky Note, then use the AI menu to analyze, transform, or enhance it.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="critical-thinking-enhancement">Critical Thinking Enhancement<a href="#critical-thinking-enhancement" class="hash-link" aria-label="Critical Thinking Enhancement的直接链接" title="Critical Thinking Enhancement的直接链接">​</a></h3>
<p>Sticky Notes excel as tools for developing deeper analytical skills:</p>
<ul>
<li>Record initial thoughts on a subject</li>
<li>Ask the AI to help with reflection exercises</li>
<li>Identify contradictions or biases in your thinking</li>
<li>Explore alternative perspectives on your ideas</li>
</ul>
<img src="/img/portfolio/fullsize/aiflow_sticky_notes_critical_thinking_tools.png" width="400" alt="FunBlocks AIFlow Sticky Notes: Critical thinking tools">
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="knowledge-exploration">Knowledge Exploration<a href="#knowledge-exploration" class="hash-link" aria-label="Knowledge Exploration的直接链接" title="Knowledge Exploration的直接链接">​</a></h3>
<p>The exploration space feature allows you to use your notes as starting points for discovering related topics and questions, expanding your understanding in any direction.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="best-practices">Best Practices<a href="#best-practices" class="hash-link" aria-label="Best Practices的直接链接" title="Best Practices的直接链接">​</a></h2>
<ul>
<li>Use Sticky Notes to preserve human-edited versions of AI content</li>
<li>Clip key points from AI-generated content into Notes</li>
<li>Keep notes concise and focused on single topics when possible</li>
<li>Consider Sticky Notes as the &quot;thinking space&quot; in your AIFlow projects</li>
</ul>
<p>By mastering Sticky Notes in FunBlocks AIFlow, you&#x27;ll significantly enhance your ability to capture, process, and develop ideas with AI assistance.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="文件选项卡"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/docs/aiflow-tricks-and-tips/Reflection"><div class="pagination-nav__sublabel">上一页</div><div class="pagination-nav__label">Optimizing AI Output</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/docs/aiflow-tricks-and-tips/Image-Node"><div class="pagination-nav__sublabel">下一页</div><div class="pagination-nav__label">Mastering the Image Node</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#what-are-sticky-notes" class="table-of-contents__link toc-highlight">What Are Sticky Notes?</a></li><li><a href="#getting-started-with-sticky-notes" class="table-of-contents__link toc-highlight">Getting Started with Sticky Notes</a></li><li><a href="#key-features-and-use-cases" class="table-of-contents__link toc-highlight">Key Features and Use Cases</a><ul><li><a href="#content-processing" class="table-of-contents__link toc-highlight">Content Processing</a></li><li><a href="#importing-external-content" class="table-of-contents__link toc-highlight">Importing External Content</a></li><li><a href="#critical-thinking-enhancement" class="table-of-contents__link toc-highlight">Critical Thinking Enhancement</a></li><li><a href="#knowledge-exploration" class="table-of-contents__link toc-highlight">Knowledge Exploration</a></li></ul></li><li><a href="#best-practices" class="table-of-contents__link toc-highlight">Best Practices</a></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>