<!doctype html>
<html lang="zh" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Infinite Canvas | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Infinite Canvas | FunBlocks AI"><meta data-rh="true" name="description" content="In today&#x27;s world of AI assistants, most conversations happen in a simple chat box - you type, the AI responds, and the conversation flows downward in a linear fashion. But FunBlocks AIFlow completely transforms this experience by placing your AI conversations on an infinite canvas. Here&#x27;s why this matters and how to make the most of it."><meta data-rh="true" property="og:description" content="In today&#x27;s world of AI assistants, most conversations happen in a simple chat box - you type, the AI responds, and the conversation flows downward in a linear fashion. But FunBlocks AIFlow completely transforms this experience by placing your AI conversations on an infinite canvas. Here&#x27;s why this matters and how to make the most of it."><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.2b6e2e3c.css">
<script src="/zh/assets/js/runtime~main.01201d88.js" defer="defer"></script>
<script src="/zh/assets/js/main.36e42d00.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/aiflow_question_box.png"><link rel="preload" as="image" href="/img/portfolio/fullsize/aiflow_exploration_space.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="回到顶部" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="文档侧边栏" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/zh/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="展开侧边栏分类 &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/zh/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="折叠侧边栏分类 &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/ai-tools">AI Tools</a><button aria-label="展开侧边栏分类 &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="页面路径"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="主页面" class="breadcrumbs__link" href="/zh/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/zh/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Infinite Canvas</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">本页总览</button></div><div class="theme-doc-markdown markdown"><header><h1>Unlimited Exploration on an Infinite Canvas</h1></header>
<p>In today&#x27;s world of AI assistants, most conversations happen in a simple chat box - you type, the AI responds, and the conversation flows downward in a linear fashion. But FunBlocks AIFlow completely transforms this experience by placing your AI conversations on an infinite canvas. Here&#x27;s why this matters and how to make the most of it.</p>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow: Visualize Your Interaction with LLM, Elevating Beyond ChatGPT" src="/zh/assets/images/aiflow_benefits-712ce5fed482211ed101e77c081ebc70.png" width="2410" height="1532" class="img_ev3q"></p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="why-use-an-infinite-canvas-instead-of-a-traditional-chat-box">Why Use an Infinite Canvas Instead of a Traditional Chat Box?<a href="#why-use-an-infinite-canvas-instead-of-a-traditional-chat-box" class="hash-link" aria-label="Why Use an Infinite Canvas Instead of a Traditional Chat Box?的直接链接" title="Why Use an Infinite Canvas Instead of a Traditional Chat Box?的直接链接">​</a></h2>
<p>Traditional chat interfaces like ChatGPT confine your thinking to a single linear conversation. Each exchange builds only on what came immediately before, narrowing your focus rather than expanding it. This limitation creates what experts call an &quot;information bubble&quot; - you only explore one path of thought at a time.</p>
<p>FunBlocks AIFlow breaks these constraints. By visualizing your conversation on an infinite canvas, you can:</p>
<ul>
<li>Explore multiple perspectives on the same topic</li>
<li>Pursue multiple threads of inquiry simultaneously</li>
<li>Explore topics in any direction without losing context</li>
<li>See connections between different ideas</li>
<li>Build a network of knowledge rather than a linear transcript</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="how-to-master-unlimited-exploration-in-aiflow">How to Master Unlimited Exploration in AIFlow<a href="#how-to-master-unlimited-exploration-in-aiflow" class="hash-link" aria-label="How to Master Unlimited Exploration in AIFlow的直接链接" title="How to Master Unlimited Exploration in AIFlow的直接链接">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-follow-up-questions-after-each-answer">1. Follow-up Questions After Each Answer<a href="#1-follow-up-questions-after-each-answer" class="hash-link" aria-label="1. Follow-up Questions After Each Answer的直接链接" title="1. Follow-up Questions After Each Answer的直接链接">​</a></h3>
<img src="/img/portfolio/fullsize/aiflow_question_box.png" width="680" alt="FunBlocks AIFlow question box to LLM">
<p>Unlike traditional interfaces where previous conversations scroll away, AIFlow keeps your entire thinking space visible. When you receive an answer:</p>
<ul>
<li>Ask follow-up questions directly from any response</li>
<li>Branch your conversation in multiple directions</li>
<li>Return to earlier points to explore new angles</li>
<li>Compare different responses visually across your canvas</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-generate-exploration-spaces">2. Generate Exploration Spaces<a href="#2-generate-exploration-spaces" class="hash-link" aria-label="2. Generate Exploration Spaces的直接链接" title="2. Generate Exploration Spaces的直接链接">​</a></h3>
<img src="/img/portfolio/fullsize/aiflow_exploration_space.png" width="680" alt="FunBlocks AIFlow related question and topic generation for exploration">
<p>This is where AIFlow truly shines! The platform can automatically generate related topics and questions around your central idea, creating what we call an &quot;exploration space.&quot;</p>
<p><strong>How it works:</strong></p>
<ul>
<li>After receiving an answer, request an exploration space</li>
<li>AIFlow will generate multiple related topics and questions surrounding your main subject</li>
<li>Click on any topic to have the AI provide more detailed information about it</li>
<li>Regenerate the exploration space as often as needed to discover new perspectives</li>
</ul>
<p>This feature acts like a &quot;thought multiplier,&quot; revealing connections and possibilities you might never have considered on your own.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="breaking-free-from-information-bubbles">Breaking Free from Information Bubbles<a href="#breaking-free-from-information-bubbles" class="hash-link" aria-label="Breaking Free from Information Bubbles的直接链接" title="Breaking Free from Information Bubbles的直接链接">​</a></h2>
<p>The infinite canvas approach fundamentally changes how you interact with AI. Instead of diving deeper into a single line of inquiry (potentially reinforcing existing biases), AIFlow encourages you to:</p>
<ul>
<li>Explore multiple perspectives on the same issue</li>
<li>Discover unexpected connections between seemingly unrelated topics</li>
<li>Maintain a bird&#x27;s-eye view of your entire knowledge exploration</li>
<li>Prevent tunnel vision by constantly seeing alternative paths</li>
</ul>
<p>By visualizing your conversation as an expanding network rather than a linear chat, AIFlow helps you build a more comprehensive understanding of any subject you&#x27;re exploring.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="getting-started">Getting Started<a href="#getting-started" class="hash-link" aria-label="Getting Started的直接链接" title="Getting Started的直接链接">​</a></h2>
<p>The best way to understand the power of FunBlocks AIFlow&#x27;s infinite canvas is to experience it yourself. Start with a central question or topic you&#x27;re interested in, then:</p>
<ol>
<li>Ask your initial question</li>
<li>After receiving an answer, request an exploration space around the topic</li>
<li>Click on generated topics that interest you</li>
<li>Watch as your knowledge network expands in all directions</li>
</ol>
<p>The more you use the infinite canvas, the more you&#x27;ll appreciate how it transforms AI interaction from a simple Q&amp;A tool into a genuine thought partner for exploration and discovery.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="文件选项卡"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/docs/category/aiflow-tricks-and-tips"><div class="pagination-nav__sublabel">上一页</div><div class="pagination-nav__label">AIFlow Tricks and Tips</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions"><div class="pagination-nav__sublabel">下一页</div><div class="pagination-nav__label">Communicate Effectively with AI</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#why-use-an-infinite-canvas-instead-of-a-traditional-chat-box" class="table-of-contents__link toc-highlight">Why Use an Infinite Canvas Instead of a Traditional Chat Box?</a></li><li><a href="#how-to-master-unlimited-exploration-in-aiflow" class="table-of-contents__link toc-highlight">How to Master Unlimited Exploration in AIFlow</a><ul><li><a href="#1-follow-up-questions-after-each-answer" class="table-of-contents__link toc-highlight">1. Follow-up Questions After Each Answer</a></li><li><a href="#2-generate-exploration-spaces" class="table-of-contents__link toc-highlight">2. Generate Exploration Spaces</a></li></ul></li><li><a href="#breaking-free-from-information-bubbles" class="table-of-contents__link toc-highlight">Breaking Free from Information Bubbles</a></li><li><a href="#getting-started" class="table-of-contents__link toc-highlight">Getting Started</a></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>