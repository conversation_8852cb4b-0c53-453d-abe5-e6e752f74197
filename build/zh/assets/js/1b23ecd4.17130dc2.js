"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[18311],{21099:(e,i,s)=>{s.d(i,{A:()=>t});const t={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},23177:(e,i,s)=>{s.d(i,{A:()=>l});var t=s(96540),n=s(50539);const o={mainNav:"mainNav_SYRv",headerContainer:"headerContainer_a2TU",logo:"logo_c69e",navLinks:"navLinks_Sixa",languageSelector:"languageSelector__dBh",hero:"hero_PrCm",heroContent:"heroContent_XkQ_",heroSubtitle:"heroSubtitle_ICC1",heroButtons:"heroButtons_aEqQ",heroImage:"heroImage_eIAu",btn:"btn_OEYT",btnSecondary:"btnSecondary_S33h",btnSm:"btnSm_oVT3",beyondChatgpt:"beyondChatgpt_FCrm",sectionTitle:"sectionTitle_s7rk",sectionDescription:"sectionDescription_bUKO",twoColumnGrid:"twoColumnGrid_A0Q4",benefitsContainer:"benefitsContainer_f82T",featureSection:"featureSection_YLp6",featureGrid:"featureGrid_iPVW",featureContent:"featureContent_CE7A",featureList:"featureList_XB6o",featureImage:"featureImage_VE23",imageLeft:"imageLeft_WPA6",imageRight:"imageRight_l9wy",fullWidthImage:"fullWidthImage_QGUC",multiModelAdvantage:"multiModelAdvantage_gag0",modelLogosContainer:"modelLogosContainer_ZsBe",modelLogoItem:"modelLogoItem_PSuJ",modelLogo:"modelLogo_ishV",modelName:"modelName_aa8M",advantageText:"advantageText_Dkdl",useCases:"useCases_rrcz",useCasesGrid:"useCasesGrid__MUR",useCaseCard:"useCaseCard_jbjO",useCaseIcon:"useCaseIcon_Z7j9",workspaceSection:"workspaceSection_t8km",ctaSection:"ctaSection_Z6zg",ctaButtons:"ctaButtons_3xcx",ctaBtn:"ctaBtn_tJ_L",toolsList:"toolsList_NtZD",pageSection:"pageSection_W06t"};var a=s(9303),r=s(74848);const l=function(e){let{page:i,feature:s,pointNos:l,imageElement:c,imageToRight:d,style:h}=e;const m=(0,t.useMemo)((()=>l.map((e=>({title:`${i}.${s}.point${e}.name`,description:`${i}.${s}.point${e}.description`})))),[i,s,l]),p=`${o.featureGrid} ${d?o.imageRight:o.imageLeft}`;return(0,r.jsx)("section",{className:o.featureSection,style:h,children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsx)(a.A,{as:"h2",className:o.sectionTitle,children:(0,r.jsx)(n.A,{id:`${i}.${s}.title`,children:"Beyond ChatGPT"})}),(0,r.jsx)("p",{className:o.sectionDescription,children:(0,r.jsx)(n.A,{id:`${i}.${s}.description`,children:"Discover innovative ways to engage with AI beyond text. Visualize your thoughts and AI outputs in formats that enhance your cognitive process."})}),(0,r.jsxs)("div",{className:p,children:[c,(0,r.jsx)("div",{className:o.featureContent,children:m.map(((e,i)=>(0,r.jsx)("div",{className:o.benefitItem,children:(0,r.jsxs)("div",{children:[(0,r.jsx)(a.A,{as:"h3",children:(0,r.jsx)(n.A,{id:e.title,children:e.title})}),(0,r.jsx)("p",{children:(0,r.jsx)(n.A,{id:e.description,children:e.description})})]})},i)))})]})]})})}},26167:(e,i,s)=>{s.d(i,{A:()=>c});const t="footer_m3PR",n="footerContainer_g8s3",o="footerLinks_EjWI",a="toolsGrid_N_gp",r="copyright_zlJy";var l=s(74848);const c=function(){return(0,l.jsx)("footer",{className:t,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:n,children:[(0,l.jsxs)("div",{className:o,style:{marginRight:"20px"},children:[(0,l.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,l.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,l.jsx)("ul",{children:(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,l.jsx)("div",{className:n,children:(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},46258:(e,i,s)=>{s.d(i,{A:()=>A});s(96540);var t=s(34164),n=s(50539);const o="comparisonSection_xGN0",a="responsiveContainer_LWkJ",r="sectionTitle_WEFW",l="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",h="featureHeader_MZvb",m="featureCell_N7ZK",p="funblocksHeader_W9br",g="funblocksCell_mVK7",u="comparisonNote_BsSN",f="scrollIndicator_qOFX";var x=s(9303),w=s(74848);const A=function(e){let{page:i="homepage",customData:s=null,titleTranslateId:A=null,descriptionTranslateId:j=null,noteTranslateId:k=null,competitors:_=null}=e;const b={funblocks:{label:(0,w.jsx)(n.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,w.jsx)(n.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,w.jsx)(n.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,w.jsx)(n.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},I=_||b,y=[{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,w.jsx)(n.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],v=s||y;return(0,w.jsx)("section",{id:"comparison",className:o,children:(0,w.jsxs)("div",{className:(0,t.A)("container",a),children:[(0,w.jsx)(x.A,{as:"h2",className:r,children:(0,w.jsx)(n.A,{id:A||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,w.jsx)("p",{className:l,children:(0,w.jsx)(n.A,{id:j||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,w.jsx)("div",{className:f,children:(0,w.jsx)(n.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,w.jsx)("div",{className:c,children:(0,w.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(I).length},children:[(0,w.jsx)("thead",{children:(0,w.jsxs)("tr",{children:[(0,w.jsx)("th",{className:h,children:(0,w.jsx)(n.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(I).map((e=>{let[i,s]=e;return(0,w.jsx)("th",{className:s.isHighlighted?p:void 0,children:s.label},i)}))]})}),(0,w.jsx)("tbody",{children:v.map(((e,i)=>(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{className:m,children:e.feature}),Object.entries(I).map((i=>{let[s,t]=i;return(0,w.jsx)("td",{className:t.isHighlighted?g:void 0,children:!0===e[s]?"\u2705":!1===e[s]?"\u274c":e[s]},s)}))]},i)))})]})}),(0,w.jsx)("div",{className:u,children:(0,w.jsx)("p",{children:(0,w.jsx)(n.A,{id:k||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,s)=>{s.d(i,{A:()=>u});var t=s(96540),n=s(50539);const o="sectionTitle_pRDY",a="sectionDescription_GyST",r="benefitsContainer_jm1z",l="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",h="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var p=s(9303),g=(s(56289),s(74848));const u=function(e){let{page:i,avatars:s}=e;const u=(0,t.useMemo)((()=>s.map(((e,s)=>({avatar:e,nameId:`${i}.testimonials.user${s+1}.name`,roleId:`${i}.testimonials.user${s+1}.role`,textId:`${i}.testimonials.user${s+1}.text`})))),[i,s]);return(0,g.jsx)("section",{id:"testimonials",className:l,children:(0,g.jsxs)("div",{className:"container",children:[(0,g.jsx)(p.A,{as:"h2",className:o,children:(0,g.jsx)(n.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,g.jsx)("p",{className:a,children:(0,g.jsx)(n.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,g.jsx)("div",{className:r,children:u?.map(((e,i)=>(0,g.jsxs)("div",{className:c,children:[(0,g.jsxs)("div",{className:d,children:[(0,g.jsx)("div",{className:h,children:(0,g.jsx)("span",{children:e.avatar})}),(0,g.jsxs)("div",{className:m,children:[(0,g.jsx)("h4",{children:(0,g.jsx)(n.A,{id:e.nameId,children:e.nameId})}),(0,g.jsx)("p",{children:(0,g.jsx)(n.A,{id:e.roleId,children:e.roleId})})]})]}),(0,g.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,g.jsx)("p",{children:(0,g.jsx)(n.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},78905:(e,i,s)=>{s.d(i,{A:()=>m});s(96540);var t=s(34164),n=s(50539);const o="btn_4iM2",a="ctaButtons_Cfhe",r="ctaBtn_Hq_p",l="ctaSection_vQl5";var c=s(9303),d=s(56289),h=s(74848);const m=function(e){let{page:i,toUrl:s,toApp:m,customButtonText:p}=e;return(0,h.jsx)("section",{id:"cta",className:l,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(c.A,{as:"h2",children:(0,h.jsx)(n.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(n.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:a,children:(0,h.jsx)(d.A,{className:(0,t.A)(o,r),to:s,onClick:s?void 0:()=>m(),children:p||(0,h.jsx)(n.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,s)=>{s.d(i,{A:()=>c});s(96540);var t=s(50539);const n="modal_osiT",o="modalImage_HWh8",a="close_Y6T6",r="zoomIndicator_r4Py";var l=s(74848);const c=function(e){let{imageSrc:i,setImageSrc:s}=e;const c=()=>{s(null)};return(0,l.jsxs)("div",{className:n,style:{display:"flex"},onClick:c,children:[(0,l.jsx)("span",{className:a,onClick:c,children:"\xd7"}),(0,l.jsx)("img",{className:o,src:i,alt:(0,t.T)({id:"modal.alt",message:"Enlarged view"})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)(t.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,s)=>{s.d(i,{A:()=>n});s(96540);var t=s(74848);const n=function(e){let{page:i}=e;const s=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",n=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${s?"source="+s+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("script",{dangerouslySetInnerHTML:{__html:n}})})}},87263:(e,i,s)=>{s.d(i,{A:()=>f});var t=s(96540),n=s(34164),o=s(50539);const a="sectionTitle_gwu3",r="faqSection_DBlu",l="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",p="faqAnswer_HbCX";var g=s(74848);function u(e){let{page:i,questionId:s,answerId:a}=e;const[r,l]=(0,t.useState)(!1);return(0,g.jsxs)("div",{className:(0,n.A)(c,{[m]:r}),children:[(0,g.jsxs)("div",{className:d,onClick:()=>{l(!r)},children:[(0,g.jsx)("span",{style:{fontWeight:"normal"},children:(0,g.jsx)(o.A,{id:`${i}.faq.${s}`})}),(0,g.jsx)("div",{className:h,style:{transform:r?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,g.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:r?"block":"none"},children:(0,g.jsx)(o.A,{id:`${i}.faq.${a}`})})]})}const f=function(e){let{page:i,faqIds:s}=e;return(0,g.jsx)("section",{id:"faqs",className:(0,n.A)("page-section",r),style:{backgroundColor:"var(--gray)"},children:(0,g.jsxs)("div",{className:"container",children:[(0,g.jsx)("h2",{className:a,children:(0,g.jsx)(o.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,g.jsx)("div",{className:l,children:s.map((e=>(0,g.jsx)(u,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}},96398:(e,i,s)=>{s.r(i),s.d(i,{default:()=>P});var t=s(96540),n=s(34164),o=s(56289),a=s(40797),r=s(30300),l=s(50539),c=s(9303),d=s(21099),h=s(26167),m=s(87263),p=s(79912),g=s(81896),u=s(51971),f=s(23177),x=s(78905),w=s(46258);const A="thinkingProcessSection_aEUY",j="sectionTitle_nMrz",k="sectionDescription_seXz",_="processStepsContainer_OEfS",b="processStep_gX6C",I="stepContent_PBVn",y="stepIcon__NPR",v="stepTitle_eg7z",C="stepDescription_JlSS",S="stepImageContainer_zhct",N="stepImage_QBoT";var T=s(74848);const B=function(e){let{page:i="slides",setShowImageSrc:s,titleTranslateId:t=null,descriptionTranslateId:n=null}=e;const o=[{icon:"\u26a1",titleId:`${i}.thinking_process.step1.title`,descriptionId:`${i}.thinking_process.step1.description`,imageSrc:"/img/portfolio/thumbnails/aiflow_productivity.png",fullImageSrc:"/img/portfolio/fullsize/aiflow_brainstorming.png",imageAlt:"Brainstorming ideas with AIFlow mind mapping"},{icon:"\ud83d\udd0d",titleId:`${i}.thinking_process.step3.title`,descriptionId:`${i}.thinking_process.step3.description`,imageSrc:"/img/portfolio/thumbnails/aiflow_critical_tools.png",fullImageSrc:"/img/portfolio/fullsize/aiflow_critical_tools.png",imageAlt:"Critical thinking analysis with AIFlow"},{icon:"\ud83d\uddfa\ufe0f",titleId:`${i}.thinking_process.step2.title`,descriptionId:`${i}.thinking_process.step2.description`,imageSrc:"/img/portfolio/thumbnails/aiflow_group_nodes.png",fullImageSrc:"/img/portfolio/fullsize/aiflow_group_nodes.png",imageAlt:"Organizing ideas with mind mapping"},{icon:"\u2728",titleId:`${i}.thinking_process.step4.title`,descriptionId:`${i}.thinking_process.step4.description`,imageSrc:"/img/portfolio/thumbnails/slides.png",fullImageSrc:"/img/portfolio/fullsize/slides.png",imageAlt:"Creating slides from organized ideas"}];return(0,T.jsx)("section",{id:"thinking-process",className:A,children:(0,T.jsxs)("div",{className:"container",children:[(0,T.jsx)(c.A,{as:"h2",className:j,children:(0,T.jsx)(l.A,{id:t||`${i}.thinking_process.title`,children:"From Brainstorming to Presentation: The Complete Thinking Process"})}),(0,T.jsx)("p",{className:k,children:(0,T.jsx)(l.A,{id:n||`${i}.thinking_process.description`,children:"FunBlocks AI provides a seamless journey from initial ideas to polished presentations, enhancing both critical and creative thinking along the way"})}),(0,T.jsx)("div",{className:_,children:o.map(((e,i)=>(0,T.jsxs)("div",{className:b,children:[(0,T.jsxs)("div",{className:I,children:[(0,T.jsx)("div",{className:y,children:e.icon}),(0,T.jsx)(c.A,{as:"h3",className:v,children:(0,T.jsx)(l.A,{id:e.titleId,children:"Step Title"})}),(0,T.jsx)("p",{className:C,children:(0,T.jsx)(l.A,{id:e.descriptionId,children:"Step description goes here"})})]}),(0,T.jsx)("div",{className:S,onClick:()=>s&&s(e.fullImageSrc),children:(0,T.jsx)("img",{src:e.imageSrc,alt:e.imageAlt,className:N})})]},i)))})]})})};var F=s(68154);const M=function(){const{siteConfig:e}=(0,a.A)(),{url:i}=e;return(0,T.jsxs)(F.m,{children:[(0,T.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Slides",applicationCategory:"ProductivityApplication, PresentationApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"Create stunning, content-focused presentations effortlessly with FunBlocks AI Slides. Our AI-powered tool combines Markdown simplicity with intelligent design for maximum impact. Ideal for busy professionals, educators, and students.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.7",ratingCount:"1850"},keywords:"AI slides, AI presentation, brainstorming, mindmap, critical thinking, creative thinking, markdown slides, presentation tool, AI-powered slides, educational slides, professional presentations"})}),(0,T.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AI Slides?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Slides is an intelligent presentation tool that combines the simplicity of Markdown with AI-powered content generation. It helps you create professional, content-focused presentations quickly and efficiently, with features like speaker notes generation, multiple themes, and online sharing."}},{"@type":"Question",name:"How does FunBlocks AI Slides enhance critical thinking?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Slides enhances critical thinking by integrating with our brainstorming and mind mapping tools, allowing you to organize complex ideas visually before transforming them into clear, structured presentations. The AI assistant helps analyze content for logical flow and suggests improvements to strengthen your arguments."}},{"@type":"Question",name:"Can FunBlocks AI Slides help with creative thinking and brainstorming?",acceptedAnswer:{"@type":"Answer",text:"Yes! FunBlocks AI Slides is part of our integrated workspace that includes powerful brainstorming and mind mapping tools. You can generate ideas visually with AIFlow, then seamlessly convert your creative concepts into professional slides with one click, maintaining the creative structure of your thoughts."}},{"@type":"Question",name:"How does FunBlocks AI Slides compare to traditional presentation software?",acceptedAnswer:{"@type":"Answer",text:"Unlike traditional presentation software that focuses on design elements, FunBlocks AI Slides prioritizes content creation with a Markdown-based approach. It integrates AI assistance for content generation and optimization, connects with mind mapping for better idea organization, and offers a distraction-free environment that emphasizes your message over complex formatting."}}]})}),(0,T.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"HowTo",name:"How to Create Effective Presentations with FunBlocks AI Slides",description:"Learn how to create professional presentations quickly using FunBlocks AI Slides with brainstorming, mind mapping, and AI assistance.",step:[{"@type":"HowToStep",name:"Brainstorm Your Ideas",text:"Use FunBlocks AIFlow to brainstorm and generate ideas for your presentation topic."},{"@type":"HowToStep",name:"Organize with Mind Mapping",text:"Structure your ideas into a logical flow using the mind mapping features."},{"@type":"HowToStep",name:"Convert to Slides",text:"Transform your mind map into slides with one click, maintaining your thought structure."},{"@type":"HowToStep",name:"Enhance with AI",text:"Use the AI assistant to improve content, generate speaker notes, and optimize your presentation."}],totalTime:"PT30M"})})]})};function L(e){let{toApp:i}=e;return(0,T.jsx)("section",{className:(0,n.A)(d.A.hero,d.A.pageSection),style:{backgroundColor:"#c8e5f0"},children:(0,T.jsxs)("div",{className:"container",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[(0,T.jsx)(c.A,{as:"h1",className:d.A.slidesTitle,children:(0,T.jsx)(l.A,{id:"slides.masthead.title",children:"Create Smarter, Present Better"})}),(0,T.jsx)("h2",{className:d.A.slidesSubtitle,children:(0,T.jsx)(l.A,{id:"slides.masthead.subtitle",children:"Unlock your presentation potential with AI-powered slides"})}),(0,T.jsx)("div",{className:d.A.heroButtons,children:(0,T.jsx)(o.A,{className:(0,n.A)("button",d.A.btn),to:"#",onClick:()=>i(),children:(0,T.jsx)(l.A,{id:"slides.masthead.cta",children:"Try for Free"})})})]})})}function $(e){let{setShowImageSrc:i,toDemo:s}=e;return(0,T.jsx)("section",{id:"features",className:d.A.slidesFeatureSection,children:(0,T.jsxs)("div",{className:d.A.slidesContainer,children:[(0,T.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,T.jsx)(l.A,{id:"slides.features.title",children:"Key Features"})}),(0,T.jsxs)("div",{children:[[{name:"slides.features.item1.name",description:"slides.features.item1.description",image:"/img/portfolio/thumbnails/slides_markdown.png",alt:"Markdown writing feature for efficient slide creation"},{name:"slides.features.item2.name",description:"slides.features.item2.description",image:"/img/portfolio/thumbnails/slides_slash_menu.png",alt:"Slash command menu for easy and smooth editing experience"},{name:"slides.features.item3.name",description:"slides.features.item3.description",image:"/img/portfolio/thumbnails/slides.png",alt:"AI-powered content generation for high-quality slide creation"},{name:"slides.features.item4.name",description:"slides.features.item4.description",image:"/img/portfolio/thumbnails/slides_notes.png",alt:"AI-generated speaker notes for effective presentation delivery"},{name:"slides.features.item5.name",description:"slides.features.item5.description",image:"/img/portfolio/thumbnails/slides_math.png",alt:"KaTeX typesetting and code block highlighting for academic and professional presentations"},{name:"slides.features.item6.name",description:"slides.features.item6.description",image:"/img/portfolio/thumbnails/slides_presentation.png",alt:"Online sharing and presentation feature for hassle-free collaboration"},{name:"slides.features.item7.name",description:"slides.features.item7.description",image:"/img/portfolio/thumbnails/slides_themes.png",alt:"Multiple theme options for professional and versatile presentations"},{name:"slides.features.item8.name",description:"slides.features.item8.description",image:"/img/portfolio/thumbnails/slides_presentation_view.png",alt:"Presenter view with timer, next slide preview, and speaker notes"}].map(((e,s)=>(0,T.jsxs)("div",{className:d.A.slidesRow,children:[(0,T.jsx)("div",{className:(0,n.A)(d.A.slidesCol8,{[d.A.order2]:s%2==0}),children:(0,T.jsx)("img",{className:d.A.featureImage,src:e.image,alt:e.alt,onClick:()=>i(e.image.replace("thumbnails","fullsize"))})}),(0,T.jsxs)("div",{className:(0,n.A)(d.A.slidesCol4,{[d.A.order1]:s%2==0}),style:{justifyContent:"center",display:"flex",flexDirection:"column"},children:[(0,T.jsx)("i",{className:(0,n.A)("fas fa-chalkboard",d.A.slidesFeatureIcon)}),(0,T.jsx)(c.A,{as:"h3",className:d.A.cardTitle,children:(0,T.jsx)(l.A,{id:e.name,children:e.name})}),(0,T.jsx)("p",{children:(0,T.jsx)(l.A,{id:e.description,children:e.description})})]})]},s))),(0,T.jsx)("div",{className:d.A.slidesRow,children:(0,T.jsx)("div",{className:d.A.centerContainer,children:(0,T.jsx)(o.A,{className:d.A.btn,to:"#",onClick:()=>s(),children:(0,T.jsx)(l.A,{id:"slides.features.more",children:"Click to explore more features and demos"})})})})]})]})})}function q(){return(0,T.jsx)("section",{id:"platform-synergy",className:d.A.slidesAISection,style:{backgroundColor:"lightcyan"},children:(0,T.jsxs)("div",{className:d.A.slidesContainer,children:[(0,T.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,T.jsx)(l.A,{id:"slides.platform-synergy.title",children:"AI Assistant: Your Intelligent Slide Creation Partner"})}),(0,T.jsx)("h4",{className:d.A.sectionDescription,children:(0,T.jsx)(l.A,{id:"slides.platform-synergy.description",children:"Collaborate with AI to generate slides, optimize content, and refine your delivery for a truly impactful presentation"})}),(0,T.jsx)("div",{className:d.A.twoColumnGrid,children:[{name:"slides.platform-synergy.point1.name",description:"slides.platform-synergy.point1.description"},{name:"slides.platform-synergy.point2.name",description:"slides.platform-synergy.point2.description"},{name:"slides.platform-synergy.point3.name",description:"slides.platform-synergy.point3.description"},{name:"slides.platform-synergy.point4.name",description:"slides.platform-synergy.point4.description"}].map(((e,i)=>(0,T.jsxs)("div",{className:d.A.slidesCardContent,children:[(0,T.jsx)(c.A,{as:"h4",className:d.A.cardTitle,children:(0,T.jsx)(l.A,{id:e.name,children:e.name})}),(0,T.jsx)("p",{children:(0,T.jsx)(l.A,{id:e.description,children:e.description})})]},i)))})]})})}function P(){const{siteConfig:e,i18n:i}=(0,a.A)(),[s,n]=(0,t.useState)(null);function o(){return window.location.hostname.includes("funblocks")?window.location.hostname.replace("www.",""):"funblocks.net"}function c(e){window.open().location.href=e}function A(){c(`https://app.${o()}/#/login?source=slides`)}return(0,T.jsxs)(r.A,{title:(0,l.T)({id:"slides.head.title",message:"FunBlocks AI Slides: Efficient AI Presentation Tool | Brainstorming to Presentation in One Workspace"}),description:(0,l.T)({id:"slides.head.description",message:"Create stunning, content-focused presentations effortlessly with FunBlocks AI Slides. Our AI-powered tool combines brainstorming, mind mapping, and critical thinking features to help you create impactful presentations. Perfect for educators, professionals, and students."}),children:[(0,T.jsx)(M,{}),(0,T.jsx)(L,{toApp:A}),(0,T.jsxs)("main",{children:[(0,T.jsx)(f.A,{page:"slides",feature:"intro",pointNos:[1,2,3,4],imageElement:(0,T.jsx)("div",{style:{flex:4,cursor:"pointer"},children:(0,T.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/slides.png",alt:"AI Slides: Effortless slide creation with Markdown, AI, and cloud collaboration",onClick:()=>n("/img/portfolio/fullsize/slides.png")})})}),(0,T.jsx)($,{setShowImageSrc:n,toDemo:function(){c(`https://service.${o()}/present.html?hid=xslides_demo_en`)}}),(0,T.jsx)(f.A,{page:"slides",feature:"ai-assistant",pointNos:[1,2,3,4],imageToRight:!0,style:{backgroundColor:"aliceblue"},imageElement:(0,T.jsx)("div",{style:{flex:4,cursor:"pointer"},children:(0,T.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/slides.png",alt:"AI Slides: Effortless slide creation with Markdown, AI, and cloud collaboration",onClick:()=>n("/img/portfolio/fullsize/slides.png")})})}),(0,T.jsx)(w.A,{page:"slides",competitors:{funblocks:{label:(0,T.jsx)(l.A,{id:"slides.comparison.funblocksHeader",children:"FunBlocks AI Slides"}),isHighlighted:!0},powerPoint:{label:(0,T.jsx)(l.A,{id:"slides.comparison.powerPointHeader",children:"PowerPoint"}),isHighlighted:!1},googleSlides:{label:(0,T.jsx)(l.A,{id:"slides.comparison.googleSlidesHeader",children:"Google Slides"}),isHighlighted:!1},canva:{label:(0,T.jsx)(l.A,{id:"slides.comparison.canvaHeader",children:"Canva"}),isHighlighted:!1}},customData:[{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature1",children:"AI-Powered Content Generation"}),funblocks:!0,powerPoint:!1,googleSlides:"Limited",canva:"Limited"},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature2",children:"Integrated Brainstorming Tools"}),funblocks:!0,powerPoint:!1,googleSlides:!1,canva:!1},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature3",children:"Mind Mapping Integration"}),funblocks:!0,powerPoint:!1,googleSlides:!1,canva:!1},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature4",children:"Critical Thinking Enhancement"}),funblocks:!0,powerPoint:!1,googleSlides:!1,canva:!1},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature5",children:"Markdown-Based Creation"}),funblocks:!0,powerPoint:!1,googleSlides:!1,canva:!1},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature6",children:"Speaker Notes Generation"}),funblocks:!0,powerPoint:"Manual",googleSlides:"Manual",canva:"Manual"},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature7",children:"Distraction-Free Interface"}),funblocks:!0,powerPoint:!1,googleSlides:"Limited",canva:!1},{feature:(0,T.jsx)(l.A,{id:"slides.comparison.feature8",children:"One-Click Conversion from Mind Maps"}),funblocks:!0,powerPoint:!1,googleSlides:!1,canva:!1}]}),(0,T.jsx)(q,{}),(0,T.jsx)(B,{setShowImageSrc:n,page:"slides"}),(0,T.jsx)(f.A,{page:"slides",feature:"use-cases",pointNos:[1,2,3,5,6],imageToRight:!0,style:{backgroundColor:"aliceblue"},imageElement:(0,T.jsx)("div",{style:{flex:2,cursor:"pointer"},children:(0,T.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/aiflow_slides_generation.png",alt:"AI-powered slide generation process with AIFlow brainstorming and mindmap exploration",onClick:()=>n("/img/portfolio/fullsize/aiflow_slides_generation.png")})})}),(0,T.jsx)(u.A,{avatars:["\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udf93","\ud83e\uddd1\u200d\ud83d\udcbb","\ud83d\udc69\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83d\udcbc"],page:"slides"}),(0,T.jsx)(x.A,{toApp:A,page:"slides"}),(0,T.jsx)(m.A,{page:"slides",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8","q9","q10","q11","q12","q13","q14","q15","q16","q17","q18"]})]}),(0,T.jsx)(h.A,{}),s&&(0,T.jsx)(p.A,{imageSrc:s,setImageSrc:n}),(0,T.jsx)(g.A,{page:"slides"})]})}}}]);