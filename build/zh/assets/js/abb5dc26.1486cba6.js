"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[65810],{18044:(e,i,n)=>{n.d(i,{A:()=>k});n(96540);var t=n(50539);const s="socialProofSection_iQYk",a="sectionTitle_K_Fi",o="metricsContainer_WuZH",r="metricCard_3RKd",l="metricIcon_NIST",c="metricValue_qazu",d="metricLabel_Vwzg",m="companiesContainer_Rn5K",h="companiesTitle_lfNe",p="companyGrid_WPY0",u="productHuntContainer_aglb",f="badgesTitle_pxwV",g="badgesWrapper_OcIX",x="badgeLink_snWB",w="badgeImage_TCK6",j="companyItem_kt31";var b=n(9303),_=n(74848);const k=function(e){let{page:i="homepage",customCompanies:n=null,customMetrics:k=null,titleTranslateId:A=null,companiesTitleTranslateId:I=null,showProductHuntBadges:v=!0}=e;const y=[{value:"100,000+",label:(0,_.jsx)(t.A,{id:`${i}.socialProof.users`,children:"Active Users"}),icon:"\ud83d\udc65"},{value:"60%",label:(0,_.jsx)(t.A,{id:`${i}.socialProof.productivity`,children:"Productivity Increase"}),icon:"\ud83d\udcc8"},{value:"80+",label:(0,_.jsx)(t.A,{id:`${i}.socialProof.countries`,children:"Countries"}),icon:"\ud83c\udf0e"},{value:"4.8/5",label:(0,_.jsx)(t.A,{id:`${i}.socialProof.rating`,children:"User Rating"}),icon:"\u2b50"}],N=n||["Google","Amazon","Microsoft","ByteDance","Tencent","XiaoMi","MIT","IBM","Meta","Harvard University","Stanford University","Yale University"],C=k||y;return(0,_.jsx)("section",{id:"social-proof",className:s,children:(0,_.jsxs)("div",{className:"container",children:[(0,_.jsx)(b.A,{as:"h2",className:a,children:(0,_.jsx)(t.A,{id:A||`${i}.socialProof.title`,children:"Trusted by Professionals Worldwide"})}),v&&(0,_.jsxs)("div",{className:u,children:[(0,_.jsx)("p",{className:f,children:(0,_.jsx)(t.A,{id:`${i}.socialProof.productHunt`,children:"Featured on Product Hunt"})}),(0,_.jsxs)("div",{className:g,children:[(0,_.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:x,children:(0,_.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=486382&theme=dark&period=daily&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:w})}),(0,_.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:x,children:(0,_.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=486382&theme=dark&period=weekly&topic_id=204&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:w})})]})]}),(0,_.jsx)("div",{className:o,children:C.map(((e,i)=>(0,_.jsxs)("div",{className:r,children:[(0,_.jsx)("div",{className:l,children:e.icon}),(0,_.jsx)("div",{className:c,children:e.value}),(0,_.jsx)("div",{className:d,children:e.label})]},i)))}),(0,_.jsxs)("div",{className:m,children:[(0,_.jsx)("p",{className:h,children:(0,_.jsx)(t.A,{id:I||`${i}.socialProof.companies`,children:"Used by innovative teams at"})}),(0,_.jsx)("div",{className:p,children:N.map(((e,i)=>(0,_.jsx)("div",{className:j,children:e},i)))})]})]})})}},21099:(e,i,n)=>{n.d(i,{A:()=>t});const t={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},26167:(e,i,n)=>{n.d(i,{A:()=>c});const t="footer_m3PR",s="footerContainer_g8s3",a="footerLinks_EjWI",o="toolsGrid_N_gp",r="copyright_zlJy";var l=n(74848);const c=function(){return(0,l.jsx)("footer",{className:t,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:s,children:[(0,l.jsxs)("div",{className:a,style:{marginRight:"20px"},children:[(0,l.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,l.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,l.jsx)("ul",{children:(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,l.jsx)("div",{className:s,children:(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},32427:(e,i,n)=>{n.d(i,{A:()=>u});n(96540);var t=n(50539),s=n(9303);const a="benefitsSection_jOdI",o="sectionTitle_t9_h",r="sectionDescription_MTcl",l="benefitsGrid_UuKA",c="benefitCard_v8OV",d="benefitIcon_gytJ",m="benefitTitle_XlcY",h="benefitDescription_bJfg";var p=n(74848);const u=function(e){let{page:i="extension_welcome",customBenefits:n=null,backgroundColor:u="honeydew"}=e;const f=n||[{icon:"\ud83d\udd04",titleId:`${i}.benefits.benefit4.title`,title:"Seamless Integration",descriptionId:`${i}.benefits.benefit4.description`,description:"Works across all websites with context-aware tools that understand what you're doing and provide relevant assistance."},{icon:"\ud83d\ude80",titleId:`${i}.benefits.benefit1.title`,title:"Enhanced Productivity",descriptionId:`${i}.benefits.benefit1.description`,description:"Save time and work more efficiently with AI-powered tools that streamline your reading, writing, and thinking processes."},{icon:"\u26a1",titleId:`${i}.benefits.benefit2.title`,title:"Improved Critical Thinking",descriptionId:`${i}.benefits.benefit2.description`,description:"Develop stronger analytical skills with structured thinking frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking."},{icon:"\ud83d\udca1",titleId:`${i}.benefits.benefit3.title`,title:"Boosted Creativity",descriptionId:`${i}.benefits.benefit3.description`,description:"Unlock new ideas and perspectives with AI-powered brainstorming and mindmapping tools that expand your creative horizons."},{icon:"\ud83d\udcca",titleId:`${i}.benefits.benefit5.title`,title:"Visual Learning",descriptionId:`${i}.benefits.benefit5.description`,description:"Transform complex information into visual formats like mindmaps, infographics, and insight cards for better understanding and retention."},{icon:"\ud83d\udd12",titleId:`${i}.benefits.benefit6.title`,title:"Privacy & Control",descriptionId:`${i}.benefits.benefit6.description`,description:"Select your preferred AI models within a unified workflow, maximizing productivity without incurring extra costs."}];return(0,p.jsx)("section",{id:"benefits",className:a,style:{backgroundColor:u},children:(0,p.jsxs)("div",{className:"container",children:[(0,p.jsx)(s.A,{as:"h2",className:o,children:(0,p.jsx)(t.A,{id:`${i}.benefits.title`,children:"Key Benefits"})}),(0,p.jsx)("p",{className:r,children:(0,p.jsx)(t.A,{id:`${i}.benefits.description`,children:"Discover how FunBlocks AI MindMap Extension transforms your browsing experience with these powerful benefits:"})}),(0,p.jsx)("div",{className:l,children:f.map(((e,i)=>(0,p.jsxs)("div",{className:c,children:[(0,p.jsx)("div",{className:d,children:e.icon}),(0,p.jsx)(s.A,{as:"h3",className:m,children:(0,p.jsx)(t.A,{id:e.titleId,children:e.title})}),(0,p.jsx)("p",{className:h,children:(0,p.jsx)(t.A,{id:e.descriptionId,children:e.description})})]},i)))})]})})}},46258:(e,i,n)=>{n.d(i,{A:()=>j});n(96540);var t=n(34164),s=n(50539);const a="comparisonSection_xGN0",o="responsiveContainer_LWkJ",r="sectionTitle_WEFW",l="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",m="featureHeader_MZvb",h="featureCell_N7ZK",p="funblocksHeader_W9br",u="funblocksCell_mVK7",f="comparisonNote_BsSN",g="scrollIndicator_qOFX";var x=n(9303),w=n(74848);const j=function(e){let{page:i="homepage",customData:n=null,titleTranslateId:j=null,descriptionTranslateId:b=null,noteTranslateId:_=null,competitors:k=null}=e;const A={funblocks:{label:(0,w.jsx)(s.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,w.jsx)(s.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,w.jsx)(s.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,w.jsx)(s.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},I=k||A,v=[{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,w.jsx)(s.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],y=n||v;return(0,w.jsx)("section",{id:"comparison",className:a,children:(0,w.jsxs)("div",{className:(0,t.A)("container",o),children:[(0,w.jsx)(x.A,{as:"h2",className:r,children:(0,w.jsx)(s.A,{id:j||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,w.jsx)("p",{className:l,children:(0,w.jsx)(s.A,{id:b||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,w.jsx)("div",{className:g,children:(0,w.jsx)(s.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,w.jsx)("div",{className:c,children:(0,w.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(I).length},children:[(0,w.jsx)("thead",{children:(0,w.jsxs)("tr",{children:[(0,w.jsx)("th",{className:m,children:(0,w.jsx)(s.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(I).map((e=>{let[i,n]=e;return(0,w.jsx)("th",{className:n.isHighlighted?p:void 0,children:n.label},i)}))]})}),(0,w.jsx)("tbody",{children:y.map(((e,i)=>(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{className:h,children:e.feature}),Object.entries(I).map((i=>{let[n,t]=i;return(0,w.jsx)("td",{className:t.isHighlighted?u:void 0,children:!0===e[n]?"\u2705":!1===e[n]?"\u274c":e[n]},n)}))]},i)))})]})}),(0,w.jsx)("div",{className:f,children:(0,w.jsx)("p",{children:(0,w.jsx)(s.A,{id:_||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,n)=>{n.d(i,{A:()=>f});var t=n(96540),s=n(50539);const a="sectionTitle_pRDY",o="sectionDescription_GyST",r="benefitsContainer_jm1z",l="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",m="testimonialAvatar_yvW1",h="testimonialInfo_YZnM";var p=n(9303),u=(n(56289),n(74848));const f=function(e){let{page:i,avatars:n}=e;const f=(0,t.useMemo)((()=>n.map(((e,n)=>({avatar:e,nameId:`${i}.testimonials.user${n+1}.name`,roleId:`${i}.testimonials.user${n+1}.role`,textId:`${i}.testimonials.user${n+1}.text`})))),[i,n]);return(0,u.jsx)("section",{id:"testimonials",className:l,children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)(p.A,{as:"h2",className:a,children:(0,u.jsx)(s.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,u.jsx)("p",{className:o,children:(0,u.jsx)(s.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,u.jsx)("div",{className:r,children:f?.map(((e,i)=>(0,u.jsxs)("div",{className:c,children:[(0,u.jsxs)("div",{className:d,children:[(0,u.jsx)("div",{className:m,children:(0,u.jsx)("span",{children:e.avatar})}),(0,u.jsxs)("div",{className:h,children:[(0,u.jsx)("h4",{children:(0,u.jsx)(s.A,{id:e.nameId,children:e.nameId})}),(0,u.jsx)("p",{children:(0,u.jsx)(s.A,{id:e.roleId,children:e.roleId})})]})]}),(0,u.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,u.jsx)("p",{children:(0,u.jsx)(s.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},74648:(e,i,n)=>{n.d(i,{A:()=>j});n(96540);var t=n(34164),s=n(50539);const a="videoSection_rFsi",o="sectionTitle_QAn3",r="sectionDescription_TQMg",l="videoContainer_bfos",c="videoWrapper__9tu",d="videoFrame_FnQH",m="videoFeatures_TNWz",h="featureItem_IV9D",p="featureIcon_ehMe",u="videoCta_SQLf",f="btn_z1hz";var g=n(9303),x=n(56289),w=n(74848);const j=function(e){let{page:i="homepage",videoId:n="tPjuWOjpJIs",titleTranslateId:j=null,descriptionTranslateId:b=null,ctaTranslateId:_=null,ctaUrl:k="https://app.funblocks.net/#/login?source=flow",bg:A="#f0f7ff",customFeatures:I=null}=e;const v=[{icon:"\ud83d\udd0d",title:(0,w.jsx)(s.A,{id:`${i}.video.feature1.title`,children:"Explore"}),description:(0,w.jsx)(s.A,{id:`${i}.video.feature1.description`,children:"See how to explore complex topics visually with AI assistance"})},{icon:"\ud83c\udf1f",title:(0,w.jsx)(s.A,{id:`${i}.video.feature2.title`,children:"Think"}),description:(0,w.jsx)(s.A,{id:`${i}.video.feature2.description`,children:"Learn how to enhance your thinking with visual frameworks"})},{icon:"\u2728",title:(0,w.jsx)(s.A,{id:`${i}.video.feature3.title`,children:"Create"}),description:(0,w.jsx)(s.A,{id:`${i}.video.feature3.description`,children:"Discover how to transform ideas into professional deliverables"})}],y=I||v;return(0,w.jsx)("section",{id:"video-demo",className:a,style:{background:A},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(g.A,{as:"h2",className:o,children:(0,w.jsx)(s.A,{id:j||`${i}.video.title`,children:"See FunBlocks AIFlow in Action"})}),(0,w.jsx)("p",{className:r,children:(0,w.jsx)(s.A,{id:b||`${i}.video.description`,children:"Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate"})}),(0,w.jsx)("div",{className:l,children:(0,w.jsx)("div",{className:c,children:(0,w.jsx)("iframe",{className:d,src:`https://www.youtube.com/embed/${n}`,title:"FunBlocks AIFlow Demo",border:"0",style:{border:0},allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})}),(0,w.jsx)("div",{className:m,children:y.map(((e,i)=>(0,w.jsxs)("div",{className:h,children:[(0,w.jsx)("div",{className:p,children:e.icon}),(0,w.jsx)("h3",{children:e.title}),(0,w.jsx)("p",{children:e.description})]},i)))}),(0,w.jsx)("div",{className:u,children:(0,w.jsx)(x.A,{className:(0,t.A)("button",f),to:"#",onClick:()=>window.open(k,"_blank"),children:(0,w.jsx)(s.A,{id:_||`${i}.video.cta`,children:"Try It Yourself"})})})]})})}},78905:(e,i,n)=>{n.d(i,{A:()=>h});n(96540);var t=n(34164),s=n(50539);const a="btn_4iM2",o="ctaButtons_Cfhe",r="ctaBtn_Hq_p",l="ctaSection_vQl5";var c=n(9303),d=n(56289),m=n(74848);const h=function(e){let{page:i,toUrl:n,toApp:h,customButtonText:p}=e;return(0,m.jsx)("section",{id:"cta",className:l,children:(0,m.jsxs)("div",{className:"container",children:[(0,m.jsx)(c.A,{as:"h2",children:(0,m.jsx)(s.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,m.jsx)("p",{children:(0,m.jsx)(s.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,m.jsx)("div",{className:o,children:(0,m.jsx)(d.A,{className:(0,t.A)(a,r),to:n,onClick:n?void 0:()=>h(),children:p||(0,m.jsx)(s.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,n)=>{n.d(i,{A:()=>c});n(96540);var t=n(50539);const s="modal_osiT",a="modalImage_HWh8",o="close_Y6T6",r="zoomIndicator_r4Py";var l=n(74848);const c=function(e){let{imageSrc:i,setImageSrc:n}=e;const c=()=>{n(null)};return(0,l.jsxs)("div",{className:s,style:{display:"flex"},onClick:c,children:[(0,l.jsx)("span",{className:o,onClick:c,children:"\xd7"}),(0,l.jsx)("img",{className:a,src:i,alt:(0,t.T)({id:"modal.alt",message:"Enlarged view"})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)(t.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,n)=>{n.d(i,{A:()=>s});n(96540);var t=n(74848);const s=function(e){let{page:i}=e;const n=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",s=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${n?"source="+n+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("script",{dangerouslySetInnerHTML:{__html:s}})})}},82324:(e,i,n)=>{n.r(i),n.d(i,{default:()=>I});var t=n(96540),s=n(34164),a=n(56289),o=n(30300),r=n(50539),l=n(9303);n(21099);const c={hero:"hero_yEVH",heroRow:"heroRow_cNRU",heroContent:"heroContent_CmWp",heroBadge:"heroBadge_CqZd",heroSubtitle:"heroSubtitle_Taom",heroButtons:"heroButtons_Wnwg",btnPrimary:"btnPrimary_QW0M",btnSecondary:"btnSecondary_jcp4",heroStats:"heroStats_Ax2y",heroStat:"heroStat_ZjLd",heroStatNumber:"heroStatNumber_EE9x",heroStatLabel:"heroStatLabel_N62m",heroImageContainer:"heroImageContainer_Teas",heroImageWrapper:"heroImageWrapper_hoG1",heroImage:"heroImage__eLk",heroImageOverlay:"heroImageOverlay_XJyK",heroImageOverlayText:"heroImageOverlayText_HxsX",featureSection:"featureSection_PC5r",sectionHeading:"sectionHeading_LVmU",sectionTitle:"sectionTitle_c33x",sectionDescription:"sectionDescription_GZtu",workflowSteps:"workflowSteps_uSdy",workflowStep:"workflowStep_FDAQ",stepNumber:"stepNumber_SqOO",stepTitle:"stepTitle_deGF",stepDescription:"stepDescription_tpO3",featureGrid:"featureGrid_ZyQa",featureContent:"featureContent_J9Kr",featureBadge:"featureBadge_zk3h",featureTitle:"featureTitle_V2E7",featureDescription:"featureDescription_vOLI",featureList:"featureList_lAw1",featureIcon:"featureIcon_igxd",featureImageWrapper:"featureImageWrapper_YW_s",featureImageContainer:"featureImageContainer_xaXF",featureImage:"featureImage_Sxei",featureImageOverlay:"featureImageOverlay_ECPh",featureImageOverlayText:"featureImageOverlayText_mH9u",pageSection:"pageSection_ChU3"};var d=n(26167),m=n(87263),h=n(51971),p=n(79912),u=n(81896),f=n(78905),g=n(27143),x=n(74848);function w(){return(0,x.jsxs)(g.A,{children:[(0,x.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Mindmap Extension",applicationCategory:"BrowserApplication",operatingSystem:"Chrome, Edge",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},description:"Transform any web page or YouTube video into visual mind maps with AI. One-click brainstorming, critical thinking, and seamless integration with FunBlocks AIFlow.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"850",bestRating:"5",worstRating:"1"},featureList:["One-click web page mind mapping","YouTube video transcript analysis","AI-powered brainstorming","Critical thinking tools","FunBlocks AIFlow integration","Visual learning enhancement"],screenshot:"https://www.funblocks.net/img/portfolio/fullsize/ai_mindmap_hero.png",downloadUrl:"https://chromewebstore.google.com/detail/funblocks-ai-mindmap/placeholder",author:{"@type":"Organization",name:"FunBlocks",url:"https://www.funblocks.net"},publisher:{"@type":"Organization",name:"FunBlocks",url:"https://www.funblocks.net"}})}),(0,x.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is the FunBlocks AI Mindmap Extension?",acceptedAnswer:{"@type":"Answer",text:"The FunBlocks AI Mindmap Extension is a Chrome browser extension that transforms any web page or YouTube video into visual mind maps using AI. It helps with learning, research, and knowledge organization."}},{"@type":"Question",name:"How does the AI Mindmap Extension work?",acceptedAnswer:{"@type":"Answer",text:"Simply install the extension, visit any webpage or YouTube video, and click the AI Mindmap icon. The extension will automatically extract key concepts and create a structured mind map that you can save to FunBlocks AIFlow for further exploration."}},{"@type":"Question",name:"Can I use the extension with YouTube videos?",acceptedAnswer:{"@type":"Answer",text:"Yes! The extension can extract transcripts from YouTube videos and convert them into mind maps, making it perfect for educational content, lectures, and tutorials."}},{"@type":"Question",name:"Is the AI Mindmap Extension free?",acceptedAnswer:{"@type":"Answer",text:"Yes, the extension is free to download and use. New users get 30 free mind map generations, plus 10 free uses daily. It's also included in all FunBlocks AI subscription plans."}},{"@type":"Question",name:"How does it integrate with FunBlocks AIFlow?",acceptedAnswer:{"@type":"Answer",text:"The AI Mindmap Extension serves as a gateway to FunBlocks AIFlow. You can save any mind map directly to your AIFlow workspace for deeper exploration, brainstorming, and advanced thinking tools."}},{"@type":"Question",name:"What browsers are supported?",acceptedAnswer:{"@type":"Answer",text:"Currently, the extension supports Chrome and Edge browsers. We plan to expand to other browsers in the future."}}]})}),(0,x.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"Product",name:"FunBlocks AI Mindmap Extension",description:"AI-powered browser extension for creating mind maps from web content and YouTube videos",brand:{"@type":"Brand",name:"FunBlocks"},offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"850"},category:"Browser Extension",image:"https://www.funblocks.net/img/portfolio/fullsize/ai_mindmap_hero.png"})}),(0,x.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:"FunBlocks AI Mindmap Extension - Transform Web Content into Mind Maps",description:"Transform any web page or YouTube video into visual mind maps with AI. One-click brainstorming, critical thinking, and seamless integration with FunBlocks AIFlow.",url:"https://www.funblocks.net/ai-mindmap",mainEntity:{"@type":"SoftwareApplication",name:"FunBlocks AI Mindmap Extension"},breadcrumb:{"@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"https://www.funblocks.net"},{"@type":"ListItem",position:2,name:"AI Mindmap Extension",item:"https://www.funblocks.net/ai-mindmap"}]}})})]})}n(46258),n(74648);var j=n(32427),b=n(18044);function _(e){let{setShowImageSrc:i}=e;return(0,x.jsx)("section",{id:"hero",className:(0,s.A)(c.hero,c.pageSection),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:(0,x.jsx)("div",{className:"container",children:(0,x.jsxs)("div",{className:c.heroRow,children:[(0,x.jsxs)("div",{className:c.heroContent,style:{flex:1,minWidth:0},children:[(0,x.jsx)("div",{className:c.heroBadge,children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.badge",children:"NEW CHROME EXTENSION"})}),(0,x.jsx)(l.A,{as:"h1",children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.title",children:"Transform Any Web Content into Mind Maps with AI"})}),(0,x.jsx)("p",{className:c.heroSubtitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.subtitle",children:"One-click mind mapping from web pages, YouTube videos, and AI conversations. Your gateway to FunBlocks AIFlow."})}),(0,x.jsxs)("div",{className:c.heroButtons,children:[(0,x.jsx)(a.A,{className:(0,s.A)("button",c.btnPrimary),to:"https://chromewebstore.google.com/detail/funblocks-ai-mindmap/placeholder",children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.download",children:"Download Extension for FREE"})}),(0,x.jsx)(a.A,{className:(0,s.A)("button",c.btnSecondary),to:"#how-it-works",children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.learn_more",children:"See How It Works"})})]}),(0,x.jsxs)("div",{className:c.heroStats,children:[(0,x.jsxs)("div",{className:c.heroStat,children:[(0,x.jsx)("span",{className:c.heroStatNumber,children:"30+"}),(0,x.jsx)("span",{className:c.heroStatLabel,children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.stat1",children:"Free Mind Maps"})})]}),(0,x.jsxs)("div",{className:c.heroStat,children:[(0,x.jsx)("span",{className:c.heroStatNumber,children:"10"}),(0,x.jsx)("span",{className:c.heroStatLabel,children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.stat2",children:"Daily Free Uses"})})]}),(0,x.jsxs)("div",{className:c.heroStat,children:[(0,x.jsx)("span",{className:c.heroStatNumber,children:"4.8\u2605"}),(0,x.jsx)("span",{className:c.heroStatLabel,children:(0,x.jsx)(r.A,{id:"ai_mindmap.hero.stat3",children:"User Rating"})})]})]})]}),(0,x.jsx)("div",{className:c.heroImageContainer,style:{flex:1,minWidth:0,display:"flex",justifyContent:"center"},children:(0,x.jsx)("div",{className:c.heroImageWrapper,children:(0,x.jsx)("img",{className:c.heroImage,onClick:()=>i("/img/portfolio/fullsize/ai_mindmap_sidebar.png"),id:"ai-mindmap-overview",alt:"FunBlocks AI Mindmap Extension interface",src:"/img/portfolio/fullsize/ai_mindmap_sidebar.png"})})})]})})})}function k(e){let{setShowImageSrc:i}=e;return(0,x.jsx)("section",{id:"how-it-works",className:c.featureSection,children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsxs)("div",{className:c.sectionHeading,children:[(0,x.jsx)(l.A,{as:"h2",className:c.sectionTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.title",children:"How AI Mindmap Works"})}),(0,x.jsx)("p",{className:c.sectionDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.description",children:"Transform any web content into visual mind maps with just one click. Perfect for learning, research, and creative thinking."})})]}),(0,x.jsxs)("div",{className:c.workflowSteps,children:[(0,x.jsxs)("div",{className:c.workflowStep,children:[(0,x.jsx)("div",{className:c.stepNumber,children:"1"}),(0,x.jsx)(l.A,{as:"h3",className:c.stepTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step1.title",children:"Install the Extension"})}),(0,x.jsx)("p",{className:c.stepDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step1.description",children:"Add AI Mindmap to Chrome in seconds. No complex setup required."})})]}),(0,x.jsxs)("div",{className:c.workflowStep,children:[(0,x.jsx)("div",{className:c.stepNumber,children:"2"}),(0,x.jsx)(l.A,{as:"h3",className:c.stepTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step2.title",children:"Browse Any Website"})}),(0,x.jsx)("p",{className:c.stepDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step2.description",children:"Visit any webpage or YouTube video and click the AI Mindmap icon to extract content."})})]}),(0,x.jsxs)("div",{className:c.workflowStep,children:[(0,x.jsx)("div",{className:c.stepNumber,children:"3"}),(0,x.jsx)(l.A,{as:"h3",className:c.stepTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step3.title",children:"Generate Mind Maps"})}),(0,x.jsx)("p",{className:c.stepDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.step3.description",children:"Watch as AI transforms complex content into clear, visual mind maps instantly."})})]})]}),(0,x.jsxs)("div",{className:c.featureGrid,children:[(0,x.jsxs)("div",{className:c.featureContent,children:[(0,x.jsx)("div",{className:c.featureBadge,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.badge",children:"WEB CONTENT"})}),(0,x.jsx)(l.A,{as:"h3",className:c.featureTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.title",children:"One-Click Web Page Mind Mapping"})}),(0,x.jsx)("p",{className:c.featureDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.description",children:"Transform any article, blog post, or web page into a structured mind map. Perfect for research, learning, and content analysis."})}),(0,x.jsxs)("ul",{className:c.featureList,children:[(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udcc4"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.point1",children:"Automatically extract key concepts and main ideas from web pages"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83c\udfaf"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.point2",children:"Organize information hierarchically for better understanding"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udcbe"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature1.point3",children:"Save to FunBlocks AIFlow for further exploration and development"})})]})]})]}),(0,x.jsx)("div",{className:c.featureImageWrapper,children:(0,x.jsx)("div",{className:c.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/ai_mindmap_webpage.png"),children:(0,x.jsx)("img",{className:c.featureImage,id:"ai-mindmap-webpage",alt:"AI Mindmap web page content extraction",src:"/img/portfolio/fullsize/ai_mindmap_sidebar.png"})})})]}),(0,x.jsxs)("div",{className:c.featureGrid,style:{flexDirection:"row-reverse"},children:[(0,x.jsxs)("div",{className:c.featureContent,children:[(0,x.jsx)("div",{className:c.featureBadge,style:{backgroundColor:"#ff4444",color:"white"},children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.badge",children:"YOUTUBE VIDEOS"})}),(0,x.jsx)(l.A,{as:"h3",className:c.featureTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.title",children:"YouTube Video Mind Maps"})}),(0,x.jsx)("p",{className:c.featureDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.description",children:"Convert YouTube video transcripts into visual mind maps. Perfect for educational videos, lectures, and tutorials."})}),(0,x.jsxs)("ul",{className:c.featureList,children:[(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83c\udfa5"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.point1",children:"Extract key points from video transcripts automatically"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udcda"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.point2",children:"Perfect for educational content and online learning"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\u23f0"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature2.point3",children:"Save hours of note-taking with instant visual summaries"})})]})]})]}),(0,x.jsx)("div",{className:c.featureImageWrapper,children:(0,x.jsx)("div",{className:c.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/ai_mindmap_youtube.png"),children:(0,x.jsx)("img",{className:c.featureImage,id:"ai-mindmap-youtube",alt:"AI Mindmap YouTube video transcript analysis",src:"/img/portfolio/fullsize/ai_mindmap_youtube.png"})})})]}),(0,x.jsxs)("div",{className:c.featureGrid,children:[(0,x.jsxs)("div",{className:c.featureContent,children:[(0,x.jsx)("div",{className:c.featureBadge,style:{backgroundColor:"#9c27b0",color:"white"},children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.badge",children:"AI BRAINSTORMING"})}),(0,x.jsx)(l.A,{as:"h3",className:c.featureTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.title",children:"AI-Powered Brainstorming & Analysis"})}),(0,x.jsx)("p",{className:c.featureDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.description",children:"Generate mind maps from any topic, perform critical analysis, and explore complex subjects with AI assistance."})}),(0,x.jsxs)("ul",{className:c.featureList,children:[(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udca1"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.point1",children:"AI brainstorming with classic thinking models"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udd0d"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.point2",children:"Critical analysis and decision-making support"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83e\udde9"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.how_it_works.feature3.point3",children:"Break down complex topics for better understanding"})})]})]})]}),(0,x.jsx)("div",{className:c.featureImageWrapper,children:(0,x.jsx)("div",{className:c.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/ai_mindmap_brainstorming.png"),children:(0,x.jsx)("img",{className:c.featureImage,id:"ai-mindmap-brainstorming",alt:"AI Mindmap brainstorming and critical thinking",src:"/img/portfolio/fullsize/ai_mindmap_brainstorming.png"})})})]})]})})}function A(e){let{setShowImageSrc:i}=e;return(0,x.jsx)("section",{id:"aiflow-integration",className:c.featureSection,style:{backgroundColor:"#f0f8ff"},children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsxs)("div",{className:c.sectionHeading,children:[(0,x.jsx)(l.A,{as:"h2",className:c.sectionTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.title",children:"Seamless Integration with FunBlocks AIFlow"})}),(0,x.jsx)("p",{className:c.sectionDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.description",children:"AI Mindmap serves as your quick entry point to the powerful FunBlocks AIFlow ecosystem. Start anywhere, explore everywhere."})})]}),(0,x.jsxs)("div",{className:c.featureGrid,children:[(0,x.jsxs)("div",{className:c.featureContent,children:[(0,x.jsx)(l.A,{as:"h3",className:c.featureTitle,children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.gateway.title",children:"Your Gateway to AIFlow"})}),(0,x.jsx)("p",{className:c.featureDescription,children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.gateway.description",children:"Transform web browsing into active learning. Every mind map you create can be saved and expanded in FunBlocks AIFlow for deeper exploration."})}),(0,x.jsxs)("ul",{className:c.featureList,children:[(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\ude80"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.gateway.point1",children:"One-click save to FunBlocks AIFlow workspace"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udd17"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.gateway.point2",children:"Seamless transition from browser to full AIFlow experience"})})]}),(0,x.jsxs)("li",{children:[(0,x.jsx)("div",{className:c.featureIcon,children:"\ud83d\udcc8"}),(0,x.jsx)("div",{children:(0,x.jsx)(r.A,{id:"ai_mindmap.aiflow_integration.gateway.point3",children:"Continue exploration with advanced thinking tools"})})]})]})]}),(0,x.jsx)("div",{className:c.featureImageWrapper,children:(0,x.jsx)("div",{className:c.featureImageContainer,onClick:()=>i("/img/portfolio/fullsize/aiflow_benefits.png"),children:(0,x.jsx)("img",{className:c.featureImage,id:"ai-mindmap-integration",alt:"AI Mindmap integration with FunBlocks AIFlow",src:"/img/portfolio/thumbnails/aiflow_benefits.png"})})})]})]})})}function I(){const[e,i]=(0,t.useState)(null);return(0,x.jsxs)(o.A,{title:(0,r.T)({id:"ai_mindmap.head.title",message:"FunBlocks AI Mindmap Extension - Transform Web Content into Mind Maps"}),description:(0,r.T)({id:"ai_mindmap.head.description",message:"Transform any web page or YouTube video into visual mind maps with AI. One-click brainstorming, critical thinking, and seamless integration with FunBlocks AIFlow. Free Chrome extension."}),keywords:(0,r.T)({id:"ai_mindmap.head.keywords",message:"AI mindmap, mind mapping, Chrome extension, web content analysis, YouTube transcript, brainstorming, critical thinking, AIFlow, visual learning, knowledge mapping"}),children:[(0,x.jsx)(w,{}),(0,x.jsx)(_,{setShowImageSrc:i}),(0,x.jsxs)("main",{children:[(0,x.jsx)(j.A,{page:"ai_mindmap",backgroundColor:"honeydew",customBenefits:[{icon:"\u2b50",titleId:"ai_mindmap.benefits.benefit1.title",title:"Visual Learning Enhancement",descriptionId:"ai_mindmap.benefits.benefit1.description",description:"Transform complex information into visual mind maps that improve comprehension and memory retention."},{icon:"\u26a1",titleId:"ai_mindmap.benefits.benefit2.title",title:"Instant Knowledge Extraction",descriptionId:"ai_mindmap.benefits.benefit2.description",description:"Extract key insights from web pages and videos in seconds, saving hours of manual note-taking."},{icon:"\ud83c\udfaf",titleId:"ai_mindmap.benefits.benefit3.title",title:"Enhanced Focus & Clarity",descriptionId:"ai_mindmap.benefits.benefit3.description",description:"Organize scattered information into clear, structured mind maps that highlight important connections."},{icon:"\ud83d\udd04",titleId:"ai_mindmap.benefits.benefit4.title",title:"Seamless Workflow Integration",descriptionId:"ai_mindmap.benefits.benefit4.description",description:"Start with any web content and seamlessly transition to deep exploration in FunBlocks AIFlow."},{icon:"\ud83d\udca1",titleId:"ai_mindmap.benefits.benefit5.title",title:"AI-Powered Insights",descriptionId:"ai_mindmap.benefits.benefit5.description",description:"Leverage AI to discover hidden patterns and generate new ideas from existing content."},{icon:"\ud83d\udcf1",titleId:"ai_mindmap.benefits.benefit6.title",title:"Always Available",descriptionId:"ai_mindmap.benefits.benefit6.description",description:"Access powerful mind mapping capabilities wherever you browse, whenever inspiration strikes."}]}),(0,x.jsx)(k,{setShowImageSrc:i}),(0,x.jsx)(A,{setShowImageSrc:i}),(0,x.jsx)(b.A,{page:"ai-mindmap",showProductHuntBadges:!0}),(0,x.jsx)(h.A,{avatars:["\ud83d\udc68\u200d\ud83c\udf93","\ud83d\udc69\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udfeb","\ud83d\udc69\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc69\u200d\ud83c\udfeb"],page:"ai_mindmap"}),(0,x.jsx)(f.A,{toApp:function(){!function(e){window.open().location.href=e}(`https://app.${window.location.hostname.includes("funblocks")?window.location.hostname.replace("www.",""):"funblocks.net"}/#/login?source=mindmap`)},page:"ai_mindmap"}),(0,x.jsx)(m.A,{page:"ai_mindmap",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8"]})]}),(0,x.jsx)(d.A,{}),e&&(0,x.jsx)(p.A,{imageSrc:e,setImageSrc:i}),(0,x.jsx)(u.A,{page:"ai-mindmap"})]})}},87263:(e,i,n)=>{n.d(i,{A:()=>g});var t=n(96540),s=n(34164),a=n(50539);const o="sectionTitle_gwu3",r="faqSection_DBlu",l="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",m="faqArrow_irh3",h="active_RDQl",p="faqAnswer_HbCX";var u=n(74848);function f(e){let{page:i,questionId:n,answerId:o}=e;const[r,l]=(0,t.useState)(!1);return(0,u.jsxs)("div",{className:(0,s.A)(c,{[h]:r}),children:[(0,u.jsxs)("div",{className:d,onClick:()=>{l(!r)},children:[(0,u.jsx)("span",{style:{fontWeight:"normal"},children:(0,u.jsx)(a.A,{id:`${i}.faq.${n}`})}),(0,u.jsx)("div",{className:m,style:{transform:r?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,u.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:r?"block":"none"},children:(0,u.jsx)(a.A,{id:`${i}.faq.${o}`})})]})}const g=function(e){let{page:i,faqIds:n}=e;return(0,u.jsx)("section",{id:"faqs",className:(0,s.A)("page-section",r),style:{backgroundColor:"var(--gray)"},children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)("h2",{className:o,children:(0,u.jsx)(a.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,u.jsx)("div",{className:l,children:n.map((e=>(0,u.jsx)(f,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);