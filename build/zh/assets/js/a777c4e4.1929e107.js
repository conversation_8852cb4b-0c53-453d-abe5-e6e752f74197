"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[3587],{21099:(e,i,n)=>{n.d(i,{A:()=>s});const s={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},26167:(e,i,n)=>{n.d(i,{A:()=>c});const s="footer_m3PR",t="footerContainer_g8s3",o="footerLinks_EjWI",a="toolsGrid_N_gp",r="copyright_zlJy";var l=n(74848);const c=function(){return(0,l.jsx)("footer",{className:s,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:t,children:[(0,l.jsxs)("div",{className:o,style:{marginRight:"20px"},children:[(0,l.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,l.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,l.jsx)("ul",{children:(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,l.jsx)("div",{className:t,children:(0,l.jsxs)("div",{className:o,children:[(0,l.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,l.jsxs)("div",{className:a,children:[(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,l.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},32427:(e,i,n)=>{n.d(i,{A:()=>x});n(96540);var s=n(50539),t=n(9303);const o="benefitsSection_jOdI",a="sectionTitle_t9_h",r="sectionDescription_MTcl",l="benefitsGrid_UuKA",c="benefitCard_v8OV",d="benefitIcon_gytJ",h="benefitTitle_XlcY",m="benefitDescription_bJfg";var p=n(74848);const x=function(e){let{page:i="extension_welcome",customBenefits:n=null,backgroundColor:x="honeydew"}=e;const u=n||[{icon:"\ud83d\udd04",titleId:`${i}.benefits.benefit4.title`,title:"Seamless Integration",descriptionId:`${i}.benefits.benefit4.description`,description:"Works across all websites with context-aware tools that understand what you're doing and provide relevant assistance."},{icon:"\ud83d\ude80",titleId:`${i}.benefits.benefit1.title`,title:"Enhanced Productivity",descriptionId:`${i}.benefits.benefit1.description`,description:"Save time and work more efficiently with AI-powered tools that streamline your reading, writing, and thinking processes."},{icon:"\u26a1",titleId:`${i}.benefits.benefit2.title`,title:"Improved Critical Thinking",descriptionId:`${i}.benefits.benefit2.description`,description:"Develop stronger analytical skills with structured thinking frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking."},{icon:"\ud83d\udca1",titleId:`${i}.benefits.benefit3.title`,title:"Boosted Creativity",descriptionId:`${i}.benefits.benefit3.description`,description:"Unlock new ideas and perspectives with AI-powered brainstorming and mindmapping tools that expand your creative horizons."},{icon:"\ud83d\udcca",titleId:`${i}.benefits.benefit5.title`,title:"Visual Learning",descriptionId:`${i}.benefits.benefit5.description`,description:"Transform complex information into visual formats like mindmaps, infographics, and insight cards for better understanding and retention."},{icon:"\ud83d\udd12",titleId:`${i}.benefits.benefit6.title`,title:"Privacy & Control",descriptionId:`${i}.benefits.benefit6.description`,description:"Select your preferred AI models within a unified workflow, maximizing productivity without incurring extra costs."}];return(0,p.jsx)("section",{id:"benefits",className:o,style:{backgroundColor:x},children:(0,p.jsxs)("div",{className:"container",children:[(0,p.jsx)(t.A,{as:"h2",className:a,children:(0,p.jsx)(s.A,{id:`${i}.benefits.title`,children:"Key Benefits"})}),(0,p.jsx)("p",{className:r,children:(0,p.jsx)(s.A,{id:`${i}.benefits.description`,children:"Discover how FunBlocks AI MindMap Extension transforms your browsing experience with these powerful benefits:"})}),(0,p.jsx)("div",{className:l,children:u.map(((e,i)=>(0,p.jsxs)("div",{className:c,children:[(0,p.jsx)("div",{className:d,children:e.icon}),(0,p.jsx)(t.A,{as:"h3",className:h,children:(0,p.jsx)(s.A,{id:e.titleId,children:e.title})}),(0,p.jsx)("p",{className:m,children:(0,p.jsx)(s.A,{id:e.descriptionId,children:e.description})})]},i)))})]})})}},46258:(e,i,n)=>{n.d(i,{A:()=>A});n(96540);var s=n(34164),t=n(50539);const o="comparisonSection_xGN0",a="responsiveContainer_LWkJ",r="sectionTitle_WEFW",l="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",h="featureHeader_MZvb",m="featureCell_N7ZK",p="funblocksHeader_W9br",x="funblocksCell_mVK7",u="comparisonNote_BsSN",g="scrollIndicator_qOFX";var f=n(9303),w=n(74848);const A=function(e){let{page:i="homepage",customData:n=null,titleTranslateId:A=null,descriptionTranslateId:j=null,noteTranslateId:b=null,competitors:k=null}=e;const _={funblocks:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},y=k||_,I=[{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],v=n||I;return(0,w.jsx)("section",{id:"comparison",className:o,children:(0,w.jsxs)("div",{className:(0,s.A)("container",a),children:[(0,w.jsx)(f.A,{as:"h2",className:r,children:(0,w.jsx)(t.A,{id:A||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,w.jsx)("p",{className:l,children:(0,w.jsx)(t.A,{id:j||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,w.jsx)("div",{className:g,children:(0,w.jsx)(t.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,w.jsx)("div",{className:c,children:(0,w.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(y).length},children:[(0,w.jsx)("thead",{children:(0,w.jsxs)("tr",{children:[(0,w.jsx)("th",{className:h,children:(0,w.jsx)(t.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(y).map((e=>{let[i,n]=e;return(0,w.jsx)("th",{className:n.isHighlighted?p:void 0,children:n.label},i)}))]})}),(0,w.jsx)("tbody",{children:v.map(((e,i)=>(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{className:m,children:e.feature}),Object.entries(y).map((i=>{let[n,s]=i;return(0,w.jsx)("td",{className:s.isHighlighted?x:void 0,children:!0===e[n]?"\u2705":!1===e[n]?"\u274c":e[n]},n)}))]},i)))})]})}),(0,w.jsx)("div",{className:u,children:(0,w.jsx)("p",{children:(0,w.jsx)(t.A,{id:b||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,n)=>{n.d(i,{A:()=>u});var s=n(96540),t=n(50539);const o="sectionTitle_pRDY",a="sectionDescription_GyST",r="benefitsContainer_jm1z",l="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",h="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var p=n(9303),x=(n(56289),n(74848));const u=function(e){let{page:i,avatars:n}=e;const u=(0,s.useMemo)((()=>n.map(((e,n)=>({avatar:e,nameId:`${i}.testimonials.user${n+1}.name`,roleId:`${i}.testimonials.user${n+1}.role`,textId:`${i}.testimonials.user${n+1}.text`})))),[i,n]);return(0,x.jsx)("section",{id:"testimonials",className:l,children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsx)(p.A,{as:"h2",className:o,children:(0,x.jsx)(t.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,x.jsx)("p",{className:a,children:(0,x.jsx)(t.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,x.jsx)("div",{className:r,children:u?.map(((e,i)=>(0,x.jsxs)("div",{className:c,children:[(0,x.jsxs)("div",{className:d,children:[(0,x.jsx)("div",{className:h,children:(0,x.jsx)("span",{children:e.avatar})}),(0,x.jsxs)("div",{className:m,children:[(0,x.jsx)("h4",{children:(0,x.jsx)(t.A,{id:e.nameId,children:e.nameId})}),(0,x.jsx)("p",{children:(0,x.jsx)(t.A,{id:e.roleId,children:e.roleId})})]})]}),(0,x.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,x.jsx)("p",{children:(0,x.jsx)(t.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},74648:(e,i,n)=>{n.d(i,{A:()=>A});n(96540);var s=n(34164),t=n(50539);const o="videoSection_rFsi",a="sectionTitle_QAn3",r="sectionDescription_TQMg",l="videoContainer_bfos",c="videoWrapper__9tu",d="videoFrame_FnQH",h="videoFeatures_TNWz",m="featureItem_IV9D",p="featureIcon_ehMe",x="videoCta_SQLf",u="btn_z1hz";var g=n(9303),f=n(56289),w=n(74848);const A=function(e){let{page:i="homepage",videoId:n="tPjuWOjpJIs",titleTranslateId:A=null,descriptionTranslateId:j=null,ctaTranslateId:b=null,ctaUrl:k="https://app.funblocks.net/#/login?source=flow",bg:_="#f0f7ff",customFeatures:y=null}=e;const I=[{icon:"\ud83d\udd0d",title:(0,w.jsx)(t.A,{id:`${i}.video.feature1.title`,children:"Explore"}),description:(0,w.jsx)(t.A,{id:`${i}.video.feature1.description`,children:"See how to explore complex topics visually with AI assistance"})},{icon:"\ud83c\udf1f",title:(0,w.jsx)(t.A,{id:`${i}.video.feature2.title`,children:"Think"}),description:(0,w.jsx)(t.A,{id:`${i}.video.feature2.description`,children:"Learn how to enhance your thinking with visual frameworks"})},{icon:"\u2728",title:(0,w.jsx)(t.A,{id:`${i}.video.feature3.title`,children:"Create"}),description:(0,w.jsx)(t.A,{id:`${i}.video.feature3.description`,children:"Discover how to transform ideas into professional deliverables"})}],v=y||I;return(0,w.jsx)("section",{id:"video-demo",className:o,style:{background:_},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(g.A,{as:"h2",className:a,children:(0,w.jsx)(t.A,{id:A||`${i}.video.title`,children:"See FunBlocks AIFlow in Action"})}),(0,w.jsx)("p",{className:r,children:(0,w.jsx)(t.A,{id:j||`${i}.video.description`,children:"Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate"})}),(0,w.jsx)("div",{className:l,children:(0,w.jsx)("div",{className:c,children:(0,w.jsx)("iframe",{className:d,src:`https://www.youtube.com/embed/${n}`,title:"FunBlocks AIFlow Demo",border:"0",style:{border:0},allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})}),(0,w.jsx)("div",{className:h,children:v.map(((e,i)=>(0,w.jsxs)("div",{className:m,children:[(0,w.jsx)("div",{className:p,children:e.icon}),(0,w.jsx)("h3",{children:e.title}),(0,w.jsx)("p",{children:e.description})]},i)))}),(0,w.jsx)("div",{className:x,children:(0,w.jsx)(f.A,{className:(0,s.A)("button",u),to:"#",onClick:()=>window.open(k,"_blank"),children:(0,w.jsx)(t.A,{id:b||`${i}.video.cta`,children:"Try It Yourself"})})})]})})}},78905:(e,i,n)=>{n.d(i,{A:()=>m});n(96540);var s=n(34164),t=n(50539);const o="btn_4iM2",a="ctaButtons_Cfhe",r="ctaBtn_Hq_p",l="ctaSection_vQl5";var c=n(9303),d=n(56289),h=n(74848);const m=function(e){let{page:i,toUrl:n,toApp:m,customButtonText:p}=e;return(0,h.jsx)("section",{id:"cta",className:l,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(c.A,{as:"h2",children:(0,h.jsx)(t.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(t.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:a,children:(0,h.jsx)(d.A,{className:(0,s.A)(o,r),to:n,onClick:n?void 0:()=>m(),children:p||(0,h.jsx)(t.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,n)=>{n.d(i,{A:()=>c});n(96540);var s=n(50539);const t="modal_osiT",o="modalImage_HWh8",a="close_Y6T6",r="zoomIndicator_r4Py";var l=n(74848);const c=function(e){let{imageSrc:i,setImageSrc:n}=e;const c=()=>{n(null)};return(0,l.jsxs)("div",{className:t,style:{display:"flex"},onClick:c,children:[(0,l.jsx)("span",{className:a,onClick:c,children:"\xd7"}),(0,l.jsx)("img",{className:o,src:i,alt:(0,s.T)({id:"modal.alt",message:"Enlarged view"})}),(0,l.jsx)("div",{className:r,children:(0,l.jsx)(s.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,n)=>{n.d(i,{A:()=>t});n(96540);var s=n(74848);const t=function(e){let{page:i}=e;const n=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",t=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${n?"source="+n+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:t}})})}},87263:(e,i,n)=>{n.d(i,{A:()=>g});var s=n(96540),t=n(34164),o=n(50539);const a="sectionTitle_gwu3",r="faqSection_DBlu",l="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",p="faqAnswer_HbCX";var x=n(74848);function u(e){let{page:i,questionId:n,answerId:a}=e;const[r,l]=(0,s.useState)(!1);return(0,x.jsxs)("div",{className:(0,t.A)(c,{[m]:r}),children:[(0,x.jsxs)("div",{className:d,onClick:()=>{l(!r)},children:[(0,x.jsx)("span",{style:{fontWeight:"normal"},children:(0,x.jsx)(o.A,{id:`${i}.faq.${n}`})}),(0,x.jsx)("div",{className:h,style:{transform:r?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,x.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:r?"block":"none"},children:(0,x.jsx)(o.A,{id:`${i}.faq.${a}`})})]})}const g=function(e){let{page:i,faqIds:n}=e;return(0,x.jsx)("section",{id:"faqs",className:(0,t.A)("page-section",r),style:{backgroundColor:"var(--gray)"},children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsx)("h2",{className:a,children:(0,x.jsx)(o.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,x.jsx)("div",{className:l,children:n.map((e=>(0,x.jsx)(u,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}},95019:(e,i,n)=>{n.r(i),n.d(i,{default:()=>H});var s=n(96540),t=n(34164),o=n(56289),a=n(30300),r=n(50539),l=n(9303),c=n(21099),d=n(26167),h=n(87263),m=n(79912),p=n(81896),x=n(51971),u=n(78905),g=n(68154),f=n(40797),w=n(74848);const A=function(){const{siteConfig:e}=(0,f.A)(),{url:i}=e;return(0,w.jsxs)(g.m,{children:[(0,w.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Browser Extension",applicationCategory:"BrowserExtension, ProductivityApplication, AIApplication",operatingSystem:"Chrome, Edge",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"FunBlocks AI Extension: Your smart AI assistant for enhanced reading, writing, and thinking. Features AI-powered brainstorming, mindmapping, critical thinking, and creative writing tools. Compatible with ChatGPT, Claude, Gemini Pro. Boost productivity across all websites.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"50"},keywords:"AI browser extension, AI writing assistant, AI reading assistant, brainstorming tool, mindmap, critical thinking, creative thinking, productivity extension, Chrome extension, Edge extension, AI mindmapping, AI infographics, AI insight cards"})}),(0,w.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AI Browser Extension?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Browser Extension is your smart AI assistant for enhanced reading, writing, and thinking across the web. It integrates AI-powered tools for brainstorming, mindmapping, critical thinking, creative writing, and content analysis directly into your browser."}},{"@type":"Question",name:"Which browsers are supported by FunBlocks AI Extension?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Extension currently supports Google Chrome and Microsoft Edge browsers, with more browser support coming soon."}},{"@type":"Question",name:"What AI models does FunBlocks AI Extension support?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI Extension supports multiple AI models including OpenAI GPT, Anthropic Claude, and Google Gemini Pro, giving you flexibility to choose the best AI for your specific needs."}},{"@type":"Question",name:"How does FunBlocks AI enhance critical thinking?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI enhances critical thinking by providing structured frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking. These frameworks help you analyze content from multiple perspectives, identify strengths and weaknesses, and break down complex topics into fundamental components."}},{"@type":"Question",name:"Can FunBlocks AI help with brainstorming and mindmapping?",acceptedAnswer:{"@type":"Answer",text:"Yes! FunBlocks AI excels at brainstorming and mindmapping. It helps generate ideas, organize thoughts visually, and explore topics from multiple angles simultaneously. The AI assistant can suggest related concepts, help expand on ideas, and create structured visual representations of your thinking process."}}]})}),(0,w.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"HowTo",name:"How to Use FunBlocks AI Browser Extension",description:"Learn how to install and use the FunBlocks AI Browser Extension to enhance your reading, writing, and thinking experience with AI-powered brainstorming, mindmapping, and critical thinking tools.",step:[{"@type":"HowToStep",name:"Install the Extension",text:"Install FunBlocks AI Extension from the Chrome Web Store or Microsoft Edge Add-ons store."},{"@type":"HowToStep",name:"Pin to Browser Toolbar",text:"Click the extension icon in your browser's top-right corner and pin FunBlocks AI to your toolbar for easy access."},{"@type":"HowToStep",name:"Select Text for Analysis",text:"Select any text on a webpage to bring up the FunBlocks AI contextual toolbar with options for translation, explanation, and more."},{"@type":"HowToStep",name:"Use AI Writing Assistant",text:"Click the FunBlocks AI icon in text fields to access the AI writing assistant for content generation and improvement."},{"@type":"HowToStep",name:"Apply Critical Thinking Frameworks",text:"Use built-in frameworks like Six Thinking Hats, SWOT Analysis, and First Principles Thinking to analyze content critically."},{"@type":"HowToStep",name:"Create Visual Content",text:"Generate mindmaps, infographics, and insight cards to visualize complex information and enhance understanding."}],totalTime:"PT5M"})}),(0,w.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"Review",itemReviewed:{"@type":"SoftwareApplication",name:"FunBlocks AI Browser Extension"},author:{"@type":"Person",name:"Sarah K."},reviewRating:{"@type":"Rating",ratingValue:"5",bestRating:"5"},datePublished:"2024-09-15",reviewBody:"The FunBlocks AI Extension has transformed my online workflow completely. The AI writing assistant helps me draft and polish content directly on any website, while the critical thinking tools help me analyze information more deeply. I especially love how the contextual toolbar appears exactly when I need it, offering relevant AI assistance based on what I'm doing. The ability to create visual mind maps from complex articles with one click has been game-changing for content planning. This extension isn't just a tool\u2014it's like having an entire AI workspace that follows me across the web."})})]})};var j=n(32427);const b="useCasesSection_xfy2",k="sectionTitle_c9hs",_="sectionDescription_o8W8",y="useCasesGrid_Ahop",I="useCaseCard__Xp1",v="useCaseIcon_uIeS",C="useCaseTitle_ILSd",T="useCaseDescription_zUhB",N="useCaseBenefits_A08J";const S=function(e){let{page:i="extension_welcome",customCases:n=null,backgroundColor:s="aliceblue"}=e;const t=n||[{icon:"\ud83c\udf93",titleId:`${i}.useCases.case1.title`,title:"For Students",descriptionId:`${i}.useCases.case1.description`,description:"Enhance learning with AI-powered note-taking, critical analysis of study materials, and brainstorming for assignments and projects.",benefits:[`${i}.useCases.case1.benefit1`,`${i}.useCases.case1.benefit2`,`${i}.useCases.case1.benefit3`],benefitTexts:["Transform complex readings into visual mind maps","Generate structured study notes with critical thinking frameworks","Brainstorm essay topics and outline arguments"]},{icon:"\ud83d\udc69\u200d\ud83d\udcbc",titleId:`${i}.useCases.case2.title`,title:"For Professionals",descriptionId:`${i}.useCases.case2.description`,description:"Boost productivity with AI assistance for emails, reports, and research. Generate professional content and analyze information efficiently.",benefits:[`${i}.useCases.case2.benefit1`,`${i}.useCases.case2.benefit2`,`${i}.useCases.case2.benefit3`],benefitTexts:["Draft professional emails and responses in seconds","Transform meeting notes into actionable summaries","Create presentation slides from any web content"]},{icon:"\u270d\ufe0f",titleId:`${i}.useCases.case3.title`,title:"For Writers & Content Creators",descriptionId:`${i}.useCases.case3.description`,description:"Enhance creativity and productivity with AI-powered brainstorming, research assistance, and content polishing tools.",benefits:[`${i}.useCases.case3.benefit1`,`${i}.useCases.case3.benefit2`,`${i}.useCases.case3.benefit3`],benefitTexts:["Generate creative content ideas with AI brainstorming","Research topics deeply with critical thinking frameworks","Create visual infographics and insight cards"]}];return(0,w.jsx)("section",{id:"usecases",className:b,style:{backgroundColor:s},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(l.A,{as:"h2",className:k,children:(0,w.jsx)(r.A,{id:`${i}.useCases.title`,children:"Use Cases"})}),(0,w.jsx)("p",{className:_,children:(0,w.jsx)(r.A,{id:`${i}.useCases.description`,children:"FunBlocks AI adapts to diverse scenarios, enhancing your productivity and creativity across different contexts."})}),(0,w.jsx)("div",{className:y,children:t.map(((e,i)=>(0,w.jsxs)("div",{className:I,children:[(0,w.jsx)("div",{className:v,children:e.icon}),(0,w.jsx)(l.A,{as:"h3",className:C,children:(0,w.jsx)(r.A,{id:e.titleId,children:e.title})}),(0,w.jsx)("p",{className:T,children:(0,w.jsx)(r.A,{id:e.descriptionId,children:e.description})}),(0,w.jsx)("ul",{className:N,children:e.benefits.map(((i,n)=>(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:i,children:e.benefitTexts[n]})},n)))})]},i)))})]})})};var F=n(46258),B=n(74648);function $(){return(0,w.jsx)("section",{className:(0,t.A)(c.A.hero,c.A.pageSection),style:{backgroundColor:"honeydew"},children:(0,w.jsxs)("div",{className:"container",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",rowGap:30},children:[(0,w.jsx)(l.A,{as:"h1",className:"mt-0 mb-3 text-center",children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.title",children:"Enhance Your Browsing with FunBlocks AI Assistant"})}),(0,w.jsx)("h2",{className:"mt-0 mb-3",children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.desc",children:"Your AI-powered reading, writing, and thinking companion for the web"})}),(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"column"},children:[(0,w.jsx)("div",{className:"col-sm flex-column justify-content-center",children:(0,w.jsxs)("ol",{children:[(0,w.jsx)("li",{style:{fontSize:"18px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.li1",children:"Click the extension icon in your browser's top-right corner"})}),(0,w.jsx)("li",{style:{fontSize:"18px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.li2",children:"Pin FunBlocks AI to your toolbar for instant access"})}),(0,w.jsx)("li",{style:{fontSize:"18px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.li3",children:"Boost productivity with AI-powered brainstorming, critical thinking, and creative writing tools"})})]})}),(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"row",maxWidth:"600px"},children:[(0,w.jsx)("div",{style:{marginRight:"10px"},children:(0,w.jsx)("img",{className:(0,t.A)(c.A.featureImage,"shadow-lg"),src:"/img/portfolio/fullsize/pin_extension.png",alt:"Pin FunBlocks AI extension to browser toolbar for easy access to brainstorming and mindmapping tools"})}),(0,w.jsx)("div",{children:(0,w.jsx)("img",{className:(0,t.A)(c.A.featureImage,"shadow-lg"),src:"/img/portfolio/fullsize/quick_extension.png",alt:"Quick access to FunBlocks AI extension features including critical thinking frameworks and creative writing tools from browser toolbar"})})]})]}),(0,w.jsx)("div",{className:"mt-3",children:(0,w.jsx)(o.A,{className:"nav-link js-scroll-trigger",to:"#settings",children:(0,w.jsx)("span",{style:{fontSize:20,fontWeight:"bold"},children:(0,w.jsx)(r.A,{id:"extension_welcome.pin.more",children:"Discover powerful AI features for brainstorming, mindmapping, and critical thinking below \u2b07\ufe0f"})})})})]})})}function M(){return(0,w.jsx)("section",{id:"settings",className:c.A.featureSection,style:{backgroundColor:"azure"},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(l.A,{as:"h2",className:c.A.sectionTitle,children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.title",children:"AI-Powered Brainstorming & Mindmapping"})}),(0,w.jsx)("p",{className:c.A.sectionDescription,children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.subtitle",children:"Enhance your thinking with AI-assisted classic thinking models and frameworks"})}),(0,w.jsxs)("div",{className:c.A.featureGrid,children:[(0,w.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,w.jsx)("img",{className:c.A.featureImage,id:"aiflow-brainstorming",alt:"AI-powered brainstorming and mindmapping tools with Six Thinking Hats, SWOT Analysis, and First Principles Thinking frameworks in FunBlocks AI Extension",src:"/img/portfolio/thumbnails/settings_llm_provider.png"})}),(0,w.jsxs)("div",{className:c.A.featureContent,style:{flex:2},children:[(0,w.jsx)(l.A,{as:"h3",style:{paddingTop:"10px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.inbox_llms.title",children:"Structured Thinking with Classic Models"})}),(0,w.jsxs)("ul",{className:c.A.featureList,children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.inbox_llms.li1",children:"Six Thinking Hats Analysis"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.inbox_llms.li2",children:"SWOT Analysis Framework"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.inbox_llms.li3",children:"First Principles Thinking"})})]}),(0,w.jsx)(l.A,{as:"h3",style:{paddingTop:"10px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.private_llms.title",children:"Creative Output Generation"})}),(0,w.jsxs)("ul",{className:c.A.featureList,children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.private_llms.li1",children:"Dynamic Presentation Slides"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.settings.private_llms.li2",children:"Comprehensive Solution Documents"})})]})]})]})]})})}function E(){return(0,w.jsx)("section",{id:"reading",className:c.A.featureSection,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(l.A,{as:"h2",className:c.A.sectionTitle,children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.title",children:"AI-Enhanced Reading & Critical Thinking"})}),(0,w.jsx)("p",{className:c.A.sectionDescription,children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.subtitle",children:"Transform how you read and analyze content with AI-powered critical thinking tools"})}),(0,w.jsxs)("div",{className:c.A.featureGrid,children:[(0,w.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,w.jsx)("img",{className:c.A.featureImage,id:"aiflow-brainstorming",alt:"FunBlocks AI sidebar assistant interface for enhanced reading with critical thinking analysis frameworks including Six Thinking Hats, SWOT Analysis, and McKinsey Problem-Solving Method",src:"/img/portfolio/thumbnails/ai_reading_en.png"})}),(0,w.jsxs)("div",{className:c.A.featureContent,style:{flex:3},children:[(0,w.jsx)(l.A,{as:"h3",children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.critical_thinking",children:"Critical Thinking Frameworks"})}),(0,w.jsxs)("ul",{className:c.A.featureList,children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.ct1",children:"Six Thinking Hats Analysis"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.ct2",children:"SWOT Analysis Framework"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.ct3",children:"McKinsey Problem-Solving Method"})})]}),(0,w.jsx)(l.A,{as:"h3",style:{paddingTop:"10px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.desc",children:"Content Transformation Outputs"})}),(0,w.jsxs)("ul",{className:c.A.featureList,children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.li1",children:"Professional Presentation Slides"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.li2",children:"Comprehensive Solution Documents"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.li3",children:"Visual Infographics & Insight Cards"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.reading.li4",children:"AI-Generated Explanatory Images"})})]})]})]})]})})}function L(){return(0,w.jsx)("section",{id:"contextual",className:c.A.featureSection,style:{backgroundColor:"azure"},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(l.A,{as:"h2",className:c.A.sectionTitle,children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.title",children:"Contextual AI Tools & Smart Widgets"})}),(0,w.jsx)("p",{className:c.A.sectionDescription,children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.subtitle",children:"Access powerful AI features instantly with context-aware toolbars and smart widgets"})}),(0,w.jsxs)("div",{className:c.A.featureGrid,children:[(0,w.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,w.jsx)("img",{className:c.A.featureImage,src:"/img/portfolio/fullsize/contextual_toolbar.png",alt:"FunBlocks AI contextual toolbar for quick text-based actions including brainstorming, mindmapping, critical thinking analysis, translation, explanation, and text polishing"})}),(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"column",justifyContent:"flex-end",flex:3,textAlign:"end"},children:[(0,w.jsx)(l.A,{as:"h3",style:{paddingTop:"10px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.toolbar",children:"Smart Contextual Toolbar"})}),(0,w.jsx)("span",{className:"text-right",children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.toolbar_desc",children:"Translate, explain, polish, and continue writing with one click - AI assistant always at the ready for any selected text"})}),(0,w.jsx)(l.A,{as:"h4",style:{paddingTop:"10px",color:"dodgerblue"},children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.try_now",children:"Try it now, select the text below:"})}),(0,w.jsx)("span",{className:"mb-3",style:{backgroundColor:"white",padding:"5px 10px",borderRadius:"8px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.select_text",children:"FunBlocks AI: Your all-in-one smart reading and writing assistant with brainstorming and critical thinking tools"})})]})]}),(0,w.jsxs)("div",{className:c.A.featureGrid,style:{flexDirection:"row",width:"100%",gap:"2rem",marginTop:"6rem"},children:[(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"end",gap:10,flex:4},children:[(0,w.jsx)("img",{className:(0,t.A)(c.A.featureImage,"mb-3","shadow-lg"),style:{width:"400px"},src:"/img/portfolio/fullsize/contextual_widget_email.png",alt:"FunBlocks AI email widget for generating professional replies in Gmail with AI-powered brainstorming and critical thinking assistance"}),(0,w.jsx)("img",{className:(0,t.A)(c.A.featureImage,"mb-2","shadow-lg"),style:{width:"400px"},src:"/img/portfolio/fullsize/contextual_widget_video.png",alt:"FunBlocks AI video widget for summarizing and analyzing YouTube content with critical thinking frameworks including Six Thinking Hats and SWOT Analysis"})]}),(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"column",flex:3},children:[(0,w.jsx)(l.A,{as:"h3",style:{paddingTop:"10px"},children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.widget",children:"Intelligent Context-Aware Widgets"})}),(0,w.jsx)("span",{className:"text-right",children:(0,w.jsx)(r.A,{id:"extension_welcome.contextual.widget_desc",children:"Smart widgets automatically appear when needed - email assistant for Gmail, video summarizer for YouTube, and more specialized tools across the web"})})]})]})]})})}function W(){return(0,w.jsx)("section",{id:"writing",className:c.A.featureSection,style:{backgroundColor:"floralwhite"},children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(l.A,{as:"h2",className:c.A.sectionTitle,children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.title",children:"AI-Powered Writing & Content Creation"})}),(0,w.jsx)("p",{className:c.A.sectionDescription,children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.subtitle",children:"Enhance your writing with intelligent AI assistance for any text field across the web"})}),(0,w.jsxs)("div",{className:c.A.featureGrid,children:[(0,w.jsx)("div",{className:"d-flex flex-column align-items-end mb-3",style:{flex:2},children:(0,w.jsx)("textarea",{id:"editor_demo",rows:15,style:{width:"100%",outline:"none",padding:"8px"},defaultValue:"Try FunBlocks AI Writing Assistant - your creative partner for brainstorming, drafting, and polishing content with critical thinking frameworks"})}),(0,w.jsxs)("div",{className:"d-flex flex-column align-items-start justify-content-between",children:[(0,w.jsxs)("div",{children:[(0,w.jsx)("h3",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.methods",children:"Multiple Ways to Access AI Writing Tools:"})}),(0,w.jsxs)("ul",{style:{fontSize:"18px"},children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.li1",children:"Select text to activate the smart contextual toolbar"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.li2",children:"Click the FunBlocks AI icon in any text field"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.li3",children:"Type '/' for quick command access to all writing tools"})})]})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("h3",{children:(0,w.jsx)(r.A,{id:"writing.features",children:"Advanced Writing Capabilities:"})}),(0,w.jsxs)("ul",{style:{fontSize:"18px"},children:[(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.f1",children:"AI-powered article generation with structured thinking frameworks"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.f2",children:"Professional text polishing with tone and style adjustment"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.f3",children:"Smart content expansion with brainstorming capabilities"})}),(0,w.jsx)("li",{children:(0,w.jsx)(r.A,{id:"extension_welcome.writing.f4",children:"Context-aware responses for emails, social media, and forums"})})]})]})]})]})]})})}function H(){const[e,i]=(0,s.useState)(null),[n,t]=(0,s.useState)("Browser");(0,s.useEffect)((()=>{t((()=>{const e=navigator.userAgent;return e.includes("Edg")?"Edge":e.includes("Chrome")?"Chrome":"Browser"})())}),[]);return(0,w.jsxs)(a.A,{title:(0,r.T)({id:"head.title",message:"FunBlocks AI Browser Extension - AI-Powered Brainstorming, Mindmapping & Critical Thinking Tools | Boost Productivity"}),description:(0,r.T)({id:"head.description",message:"Transform your browsing with FunBlocks AI Extension. Features AI-powered brainstorming, mindmapping, critical thinking frameworks (Six Thinking Hats, SWOT Analysis), and creative writing tools. Generate infographics, insight cards, and visual mind maps. Compatible with ChatGPT, Claude, Gemini Pro. Enhance how you read, write, and think online."}),children:[(0,w.jsx)(A,{}),(0,w.jsx)($,{}),(0,w.jsx)(j.A,{page:"extension_welcome"}),(0,w.jsx)(M,{setShowImageSrc:i}),(0,w.jsx)(E,{}),(0,w.jsx)(L,{}),(0,w.jsx)(W,{}),(0,w.jsx)(F.A,{page:"extension_welcome",competitors:{funblocks:{label:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.funblocksHeader",children:"FunBlocks AI"}),isHighlighted:!0},monica:{label:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.chatgptHeader",children:"Monica"}),isHighlighted:!1},grammarly:{label:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.grammarlyHeader",children:"Grammarly"}),isHighlighted:!1},other:{label:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.otherHeader",children:"Other AI Extensions"}),isHighlighted:!1}},customData:[{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature1",children:"AI-Powered Brainstorming"}),funblocks:!0,monica:!1,grammarly:!1,other:"Limited"},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature2",children:"Visual Mind Mapping"}),funblocks:!0,monica:"Limited",grammarly:!1,other:!1},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature3",children:"Critical Thinking Frameworks"}),funblocks:!0,monica:!1,grammarly:!1,other:"Limited"},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature4",children:"Contextual AI Toolbar"}),funblocks:!0,monica:!0,grammarly:"Limited",other:"Limited"},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature5",children:"Infographic & Insight Card Creation"}),funblocks:!0,monica:!1,grammarly:!1,other:!1},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature6",children:"Multi-Model AI Support"}),funblocks:!0,monica:!0,grammarly:!1,other:"Limited"},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature7",children:"Context-Aware Widgets"}),funblocks:!0,monica:!0,grammarly:"Limited",other:"Limited"},{feature:(0,w.jsx)(r.A,{id:"extension_welcome.comparison.feature8",children:"Free Version Available"}),funblocks:!0,monica:!0,grammarly:!0,other:"Varies"}],noteTranslateId:"extension_welcome.comparison.note"}),(0,w.jsx)(B.A,{page:"extension_welcome",videoId:"dtLVO7hPaBY",customFeatures:[{icon:"\u270d\ufe0f",title:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature1.title",children:"AI Writing Assistant"}),description:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature1.description",children:"Enhance your writing with AI-powered tools for content creation, editing, and brainstorming"})},{icon:"\ud83d\udcda",title:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature2.title",children:"AI Reading Assistant"}),description:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature2.description",children:"Transform how you read online with AI-powered summarization, analysis, and visualization"})},{icon:"\u26a1",title:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature3.title",children:"Cognitive Boosting Tools"}),description:(0,w.jsx)(r.A,{id:"extension_welcome.video.feature3.description",children:"Elevate your thinking with structured frameworks, mindmapping, and critical analysis tools"})}]}),(0,w.jsx)(u.A,{page:"extension_welcome",toApp:()=>{"Edge"===n?window.open("https://microsoftedge.microsoft.com/addons/detail/funblocks-ai-your-ultim/lmmlojdklhcdiefaniakpkhhdmamnigk","_blank"):"Chrome"===n?window.open("https://chrome.google.com/webstore/detail/coodnehmocjfaandkbeknihiagfccoid","_blank"):alert("We only support Chrome and Edge browsers. It is recommended to use Chrome for the best experience.")}}),(0,w.jsx)(S,{page:"extension_welcome"}),(0,w.jsx)(x.A,{avatars:["\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udf93","\ud83e\uddd1\u200d\ud83d\udcbb","\ud83d\udc69\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83d\udcbc"],page:"extension_welcome"}),(0,w.jsx)(h.A,{page:"extension_welcome",faqIds:["q1","q2","q3","q4","q5","q8","q10","q11","q12","q13","q14","q15","q16","q17","q18"]}),(0,w.jsx)(d.A,{}),e&&(0,w.jsx)(m.A,{imageSrc:e,setImageSrc:i}),(0,w.jsx)(p.A,{page:"extension_welcome"})]})}}}]);