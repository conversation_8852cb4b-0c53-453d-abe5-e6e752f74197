"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[35876],{18044:(e,i,s)=>{s.d(i,{A:()=>b});s(96540);var n=s(50539);const t="socialProofSection_iQYk",a="sectionTitle_K_Fi",o="metricsContainer_WuZH",l="metricCard_3RKd",r="metricIcon_NIST",c="metricValue_qazu",d="metricLabel_Vwzg",h="companiesContainer_Rn5K",m="companiesTitle_lfNe",p="companyGrid_WPY0",g="productHuntContainer_aglb",u="badgesTitle_pxwV",f="badgesWrapper_OcIX",x="badgeLink_snWB",w="badgeImage_TCK6",j="companyItem_kt31";var A=s(9303),k=s(74848);const b=function(e){let{page:i="homepage",customCompanies:s=null,customMetrics:b=null,titleTranslateId:v=null,companiesTitleTranslateId:I=null,showProductHuntBadges:_=!0}=e;const y=[{value:"100,000+",label:(0,k.jsx)(n.A,{id:`${i}.socialProof.users`,children:"Active Users"}),icon:"\ud83d\udc65"},{value:"60%",label:(0,k.jsx)(n.A,{id:`${i}.socialProof.productivity`,children:"Productivity Increase"}),icon:"\ud83d\udcc8"},{value:"80+",label:(0,k.jsx)(n.A,{id:`${i}.socialProof.countries`,children:"Countries"}),icon:"\ud83c\udf0e"},{value:"4.8/5",label:(0,k.jsx)(n.A,{id:`${i}.socialProof.rating`,children:"User Rating"}),icon:"\u2b50"}],N=s||["Google","Amazon","Microsoft","ByteDance","Tencent","XiaoMi","MIT","IBM","Meta","Harvard University","Stanford University","Yale University"],C=b||y;return(0,k.jsx)("section",{id:"social-proof",className:t,children:(0,k.jsxs)("div",{className:"container",children:[(0,k.jsx)(A.A,{as:"h2",className:a,children:(0,k.jsx)(n.A,{id:v||`${i}.socialProof.title`,children:"Trusted by Professionals Worldwide"})}),_&&(0,k.jsxs)("div",{className:g,children:[(0,k.jsx)("p",{className:u,children:(0,k.jsx)(n.A,{id:`${i}.socialProof.productHunt`,children:"Featured on Product Hunt"})}),(0,k.jsxs)("div",{className:f,children:[(0,k.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:x,children:(0,k.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=486382&theme=dark&period=daily&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:w})}),(0,k.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:x,children:(0,k.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=486382&theme=dark&period=weekly&topic_id=204&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:w})})]})]}),(0,k.jsx)("div",{className:o,children:C.map(((e,i)=>(0,k.jsxs)("div",{className:l,children:[(0,k.jsx)("div",{className:r,children:e.icon}),(0,k.jsx)("div",{className:c,children:e.value}),(0,k.jsx)("div",{className:d,children:e.label})]},i)))}),(0,k.jsxs)("div",{className:h,children:[(0,k.jsx)("p",{className:m,children:(0,k.jsx)(n.A,{id:I||`${i}.socialProof.companies`,children:"Used by innovative teams at"})}),(0,k.jsx)("div",{className:p,children:N.map(((e,i)=>(0,k.jsx)("div",{className:j,children:e},i)))})]})]})})}},21099:(e,i,s)=>{s.d(i,{A:()=>n});const n={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},23177:(e,i,s)=>{s.d(i,{A:()=>r});var n=s(96540),t=s(50539);const a={mainNav:"mainNav_SYRv",headerContainer:"headerContainer_a2TU",logo:"logo_c69e",navLinks:"navLinks_Sixa",languageSelector:"languageSelector__dBh",hero:"hero_PrCm",heroContent:"heroContent_XkQ_",heroSubtitle:"heroSubtitle_ICC1",heroButtons:"heroButtons_aEqQ",heroImage:"heroImage_eIAu",btn:"btn_OEYT",btnSecondary:"btnSecondary_S33h",btnSm:"btnSm_oVT3",beyondChatgpt:"beyondChatgpt_FCrm",sectionTitle:"sectionTitle_s7rk",sectionDescription:"sectionDescription_bUKO",twoColumnGrid:"twoColumnGrid_A0Q4",benefitsContainer:"benefitsContainer_f82T",featureSection:"featureSection_YLp6",featureGrid:"featureGrid_iPVW",featureContent:"featureContent_CE7A",featureList:"featureList_XB6o",featureImage:"featureImage_VE23",imageLeft:"imageLeft_WPA6",imageRight:"imageRight_l9wy",fullWidthImage:"fullWidthImage_QGUC",multiModelAdvantage:"multiModelAdvantage_gag0",modelLogosContainer:"modelLogosContainer_ZsBe",modelLogoItem:"modelLogoItem_PSuJ",modelLogo:"modelLogo_ishV",modelName:"modelName_aa8M",advantageText:"advantageText_Dkdl",useCases:"useCases_rrcz",useCasesGrid:"useCasesGrid__MUR",useCaseCard:"useCaseCard_jbjO",useCaseIcon:"useCaseIcon_Z7j9",workspaceSection:"workspaceSection_t8km",ctaSection:"ctaSection_Z6zg",ctaButtons:"ctaButtons_3xcx",ctaBtn:"ctaBtn_tJ_L",toolsList:"toolsList_NtZD",pageSection:"pageSection_W06t"};var o=s(9303),l=s(74848);const r=function(e){let{page:i,feature:s,pointNos:r,imageElement:c,imageToRight:d,style:h}=e;const m=(0,n.useMemo)((()=>r.map((e=>({title:`${i}.${s}.point${e}.name`,description:`${i}.${s}.point${e}.description`})))),[i,s,r]),p=`${a.featureGrid} ${d?a.imageRight:a.imageLeft}`;return(0,l.jsx)("section",{className:a.featureSection,style:h,children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsx)(o.A,{as:"h2",className:a.sectionTitle,children:(0,l.jsx)(t.A,{id:`${i}.${s}.title`,children:"Beyond ChatGPT"})}),(0,l.jsx)("p",{className:a.sectionDescription,children:(0,l.jsx)(t.A,{id:`${i}.${s}.description`,children:"Discover innovative ways to engage with AI beyond text. Visualize your thoughts and AI outputs in formats that enhance your cognitive process."})}),(0,l.jsxs)("div",{className:p,children:[c,(0,l.jsx)("div",{className:a.featureContent,children:m.map(((e,i)=>(0,l.jsx)("div",{className:a.benefitItem,children:(0,l.jsxs)("div",{children:[(0,l.jsx)(o.A,{as:"h3",children:(0,l.jsx)(t.A,{id:e.title,children:e.title})}),(0,l.jsx)("p",{children:(0,l.jsx)(t.A,{id:e.description,children:e.description})})]})},i)))})]})]})})}},26167:(e,i,s)=>{s.d(i,{A:()=>c});const n="footer_m3PR",t="footerContainer_g8s3",a="footerLinks_EjWI",o="toolsGrid_N_gp",l="copyright_zlJy";var r=s(74848);const c=function(){return(0,r.jsx)("footer",{className:n,children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:t,children:[(0,r.jsxs)("div",{className:a,style:{marginRight:"20px"},children:[(0,r.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,r.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,r.jsx)("ul",{children:(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,r.jsx)("div",{className:t,children:(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,r.jsxs)("div",{className:o,children:[(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,r.jsx)("div",{className:l,children:(0,r.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},40101:(e,i,s)=>{s.d(i,{A:()=>x});s(96540);var n=s(34164),t=s(56289),a=s(50539),o=s(9303);const l="thinkingMattersSection_WOo4",r="sectionTitle_eJhx",c="sectionDescription_pO3x",d="thinkingGridContainer_l_4S",h="thinkingCard_pQuf",m="cardTitle_iexr",p="thinkingIcon_qo4E",g="learnMoreContainer_c0ZI",u="learnMoreButton_BcL4";var f=s(74848);const x=function(e){let{page:i="homepage"}=e;return(0,f.jsx)("section",{id:"thinking-matters",className:(0,n.A)(l),children:(0,f.jsxs)("div",{className:"container",children:[(0,f.jsx)(o.A,{as:"h2",className:r,children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.title`,children:"Your Thinking Matters in the Age of AI"})}),(0,f.jsx)("p",{className:c,children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.description`,children:"In today's fast-paced world filled with AI tools, effective thinking skills are more valuable than ever. FunBlocks empowers you to enhance your cognitive abilities through structured thinking methods and visualization."})}),(0,f.jsxs)("div",{className:d,children:[(0,f.jsxs)("div",{className:h,children:[(0,f.jsxs)("div",{className:m,children:[(0,f.jsx)("div",{className:p,children:"\ud83e\udded"}),(0,f.jsx)(o.A,{as:"h4",children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.master.title`,children:"Master Your Mind"})})]}),(0,f.jsx)("p",{children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.master.description`,children:"In today's information-rich world, effective thinking methods are essential. FunBlocks AIF helps you cut through noise, gain clarity, and boost efficiency by integrating proven cognitive techniques with intuitive technology."})})]}),(0,f.jsxs)("div",{className:h,children:[(0,f.jsxs)("div",{className:m,children:[(0,f.jsx)("div",{className:p,children:"\ud83e\udde9"}),(0,f.jsx)(o.A,{as:"h4",children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.concepts.title`,children:"Core Thinking Concepts"})})]}),(0,f.jsx)("p",{children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.concepts.description`,children:"Our platform is built on fundamental thinking methodologies: Brainstorming for idea generation, Mind Mapping for organization, Critical Thinking for analysis, Creative Thinking for innovation, and Mental Models for understanding."})})]}),(0,f.jsxs)("div",{className:h,children:[(0,f.jsxs)("div",{className:m,children:[(0,f.jsx)("div",{className:p,children:"\ud83c\udf1f"}),(0,f.jsx)(o.A,{as:"h4",children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.empowerment.title`,children:"AI-Enhanced Thinking"})})]}),(0,f.jsx)("p",{children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.empowerment.description`,children:"FunBlocks AIFlow enhances your thinking with a unified workspace, visualization tools, AI assistance, and seamless collaboration features\u2014all designed to help you think more deeply and effectively."})})]})]}),(0,f.jsx)("div",{className:g,children:(0,f.jsx)(t.A,{className:(0,n.A)("button",u),to:"/thinking-matters/behind-aiflow",children:(0,f.jsx)(a.A,{id:`${i}.thinking_matters.learn_more`,children:"Learn More About Our Thinking Toolkit"})})})]})})}},46258:(e,i,s)=>{s.d(i,{A:()=>j});s(96540);var n=s(34164),t=s(50539);const a="comparisonSection_xGN0",o="responsiveContainer_LWkJ",l="sectionTitle_WEFW",r="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",h="featureHeader_MZvb",m="featureCell_N7ZK",p="funblocksHeader_W9br",g="funblocksCell_mVK7",u="comparisonNote_BsSN",f="scrollIndicator_qOFX";var x=s(9303),w=s(74848);const j=function(e){let{page:i="homepage",customData:s=null,titleTranslateId:j=null,descriptionTranslateId:A=null,noteTranslateId:k=null,competitors:b=null}=e;const v={funblocks:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,w.jsx)(t.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},I=b||v,_=[{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,w.jsx)(t.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],y=s||_;return(0,w.jsx)("section",{id:"comparison",className:a,children:(0,w.jsxs)("div",{className:(0,n.A)("container",o),children:[(0,w.jsx)(x.A,{as:"h2",className:l,children:(0,w.jsx)(t.A,{id:j||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,w.jsx)("p",{className:r,children:(0,w.jsx)(t.A,{id:A||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,w.jsx)("div",{className:f,children:(0,w.jsx)(t.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,w.jsx)("div",{className:c,children:(0,w.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(I).length},children:[(0,w.jsx)("thead",{children:(0,w.jsxs)("tr",{children:[(0,w.jsx)("th",{className:h,children:(0,w.jsx)(t.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(I).map((e=>{let[i,s]=e;return(0,w.jsx)("th",{className:s.isHighlighted?p:void 0,children:s.label},i)}))]})}),(0,w.jsx)("tbody",{children:y.map(((e,i)=>(0,w.jsxs)("tr",{children:[(0,w.jsx)("td",{className:m,children:e.feature}),Object.entries(I).map((i=>{let[s,n]=i;return(0,w.jsx)("td",{className:n.isHighlighted?g:void 0,children:!0===e[s]?"\u2705":!1===e[s]?"\u274c":e[s]},s)}))]},i)))})]})}),(0,w.jsx)("div",{className:u,children:(0,w.jsx)("p",{children:(0,w.jsx)(t.A,{id:k||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,s)=>{s.d(i,{A:()=>u});var n=s(96540),t=s(50539);const a="sectionTitle_pRDY",o="sectionDescription_GyST",l="benefitsContainer_jm1z",r="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",h="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var p=s(9303),g=(s(56289),s(74848));const u=function(e){let{page:i,avatars:s}=e;const u=(0,n.useMemo)((()=>s.map(((e,s)=>({avatar:e,nameId:`${i}.testimonials.user${s+1}.name`,roleId:`${i}.testimonials.user${s+1}.role`,textId:`${i}.testimonials.user${s+1}.text`})))),[i,s]);return(0,g.jsx)("section",{id:"testimonials",className:r,children:(0,g.jsxs)("div",{className:"container",children:[(0,g.jsx)(p.A,{as:"h2",className:a,children:(0,g.jsx)(t.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,g.jsx)("p",{className:o,children:(0,g.jsx)(t.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,g.jsx)("div",{className:l,children:u?.map(((e,i)=>(0,g.jsxs)("div",{className:c,children:[(0,g.jsxs)("div",{className:d,children:[(0,g.jsx)("div",{className:h,children:(0,g.jsx)("span",{children:e.avatar})}),(0,g.jsxs)("div",{className:m,children:[(0,g.jsx)("h4",{children:(0,g.jsx)(t.A,{id:e.nameId,children:e.nameId})}),(0,g.jsx)("p",{children:(0,g.jsx)(t.A,{id:e.roleId,children:e.roleId})})]})]}),(0,g.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,g.jsx)("p",{children:(0,g.jsx)(t.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},61045:(e,i,s)=>{s.r(i),s.d(i,{default:()=>F});var n=s(96540),t=s(34164),a=s(56289),o=s(30300),l=s(50539),r=s(9303),c=s(21099),d=s(26167),h=s(87263),m=s(83890),p=s(51971),g=s(79912),u=s(81896),f=s(23177),x=s(78905),w=s(40101),j=s(63989),A=s(46258),k=s(18044),b=s(74848);function v(e){let{setShowImageSrc:i,toApp:s}=e;return(0,b.jsx)("section",{id:"hero",className:(0,t.A)(c.A.hero,c.A.pageSection),style:{backgroundColor:"#f8f8f8",backgroundImage:"radial-gradient(#ccc 1px, transparent 1px)",backgroundSize:"20px 20px",padding:"40px 20px"},children:(0,b.jsxs)("div",{className:"container",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[(0,b.jsx)(r.A,{as:"h1",style:{fontSize:"2.8rem",textAlign:"center",maxWidth:"800px"},children:(0,b.jsx)(l.A,{id:"aiflow.masthead.title_2",children:"Unlock Your Cognitive Potential with Visualized ChatGPT"})}),(0,b.jsx)("p",{className:c.A.heroSubtitle,style:{fontSize:"1.4rem",maxWidth:"800px",textAlign:"center"},children:(0,b.jsx)(l.A,{id:"aiflow.masthead.subtitle",children:"Visualize Ideas, Solve Problems, and Learn Faster with Integrated Brainstorming, Mind Mapping, Critical Thinking, and Creative Thinking Tools"})}),(0,b.jsxs)("div",{className:c.A.heroButtons,style:{marginTop:"2rem"},children:[(0,b.jsx)(a.A,{className:(0,t.A)("button",c.A.btn),to:"#",onClick:()=>s(),style:{fontSize:"1.2rem",padding:"12px 30px"},children:(0,b.jsx)(l.A,{id:"aiflow.masthead.cta",children:"Start Free Trial"})}),(0,b.jsx)(a.A,{className:(0,t.A)("button",c.A.btnSecondary),to:"#thinking-methods",style:{fontSize:"1.2rem",padding:"12px 30px",marginLeft:"15px"},children:(0,b.jsx)(l.A,{id:"aiflow.masthead.learn_more",children:"Explore Features"})})]}),(0,b.jsxs)("div",{className:c.A.thinkingMethodsContainer,children:[(0,b.jsxs)("div",{className:c.A.thinkingMethodItem,children:[(0,b.jsx)("span",{className:c.A.thinkingMethodIcon,children:"\u26a1"}),(0,b.jsx)("span",{className:c.A.thinkingMethodText,children:(0,b.jsx)(l.A,{id:"aiflow.thinking_methods.brainstorming",children:"Brainstorming"})})]}),(0,b.jsxs)("div",{className:c.A.thinkingMethodItem,children:[(0,b.jsx)("span",{className:c.A.thinkingMethodIcon,children:"\ud83d\uddfa\ufe0f"}),(0,b.jsx)("span",{className:c.A.thinkingMethodText,children:(0,b.jsx)(l.A,{id:"aiflow.thinking_methods.mind_mapping",children:"Mind Mapping"})})]}),(0,b.jsxs)("div",{className:c.A.thinkingMethodItem,children:[(0,b.jsx)("span",{className:c.A.thinkingMethodIcon,children:"\ud83d\udd0d"}),(0,b.jsx)("span",{className:c.A.thinkingMethodText,children:(0,b.jsx)(l.A,{id:"aiflow.thinking_methods.critical_thinking",children:"Critical Thinking"})})]}),(0,b.jsxs)("div",{className:c.A.thinkingMethodItem,children:[(0,b.jsx)("span",{className:c.A.thinkingMethodIcon,children:"\ud83d\udca1"}),(0,b.jsx)("span",{className:c.A.thinkingMethodText,children:(0,b.jsx)(l.A,{id:"aiflow.thinking_methods.creative_thinking",children:"Creative Thinking"})})]})]}),(0,b.jsx)("p",{style:{marginTop:"20px",fontStyle:"italic"},children:(0,b.jsx)(l.A,{id:"homepage.thinking_matters.title",children:"Your Thinking Matters in the Age of AI"})})]})})}function I(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"features",className:(0,t.A)(c.A.featureSection),style:{backgroundColor:"#F0FFF0"},children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.features.title",children:"Key Features"})}),[{nameId:"aiflow.features.item1.name",descriptionId:"aiflow.features.item1.description",listItems:[{id:"aiflow.features.item1.li1",text:"Break complex problems into manageable parts"},{id:"aiflow.features.item1.li2",text:"Rapidly identify key elements and potential challenges"},{id:"aiflow.features.item1.li3",text:"Simplify the research process through a divide-and-conquer approach"}],imageSrc:"/img/portfolio/thumbnails/aiflow_breakdown.png",imageAlt:"Illustration of AI-powered problem breakdown process"},{nameId:"aiflow.features.item2.name",descriptionId:"aiflow.features.item2.description",listItems:[{id:"aiflow.features.item2.li1",text:"Generate multiple perspectives on any topic"},{id:"aiflow.features.item2.li2",text:"Discover connections and insights you might have missed"},{id:"aiflow.features.item2.li3",text:"Let AI suggest unexpected angles and approaches"},{id:"aiflow.features.item2.li4",text:"Let AI suggest unexpected angles and approaches"}],imageSrc:"/img/portfolio/thumbnails/aiflow_expansion.png",imageAlt:"Visualization of AI-assisted thought expansion and idea generation"},{nameId:"aiflow.features.item3.name",descriptionId:"aiflow.features.item3.description",listItems:[{id:"aiflow.features.item3.li1",text:"Image: AI analysis of visuals, from art to whiteboards"},{id:"aiflow.features.item3.li2",text:"Note: Quick idea capture with AI-powered expansion"},{id:"aiflow.features.item3.li3",text:"Task list: AI-enhanced task management and prioritization"}],imageSrc:"/img/portfolio/thumbnails/aiflow_optimize_prompt.png",imageAlt:"Showcase of diverse content types supported by AI assistants"},{nameId:"aiflow.features.item4.name",descriptionId:"aiflow.features.item4.description",listItems:[{id:"aiflow.features.item4.li1",text:"Image: AI analysis of visuals, from art to whiteboards"},{id:"aiflow.features.item4.li2",text:"Note: Quick idea capture with AI-powered expansion"},{id:"aiflow.features.item4.li3",text:"Task list: AI-enhanced task management and prioritization"},{id:"aiflow.features.item4.li4",text:"Link: Web content integration for research and inspiration"}],imageSrc:"/img/portfolio/thumbnails/aiflow_image.png",imageAlt:"Showcase of diverse content types supported by AI assistants"},{nameId:"aiflow.features.item5.name",descriptionId:"aiflow.features.item5.description",listItems:[{id:"aiflow.features.item5.li1",text:"Organize related concepts into cohesive clusters for enhanced comprehension"},{id:"aiflow.features.item5.li2",text:"Leverage AI assistant support for analysis and generation"},{id:"aiflow.features.item5.li3",text:"Synthesize diverse ideas into comprehensive solutions with one-click"}],imageSrc:"/img/portfolio/thumbnails/aiflow_group_nodes.png",imageAlt:"Demonstration of AI-powered group nodes for organizing and synthesizing ideas"},{nameId:"aiflow.features.item6.name",descriptionId:"aiflow.features.item6.description",listItems:[{id:"aiflow.features.item6.li1",text:"Provide intelligent analysis and suggestions to enhance thinking quality"},{id:"aiflow.features.item6.li2",text:"Automatically generate high-quality content, saving time and effort"},{id:"aiflow.features.item6.li3",text:"Support personalized AI instructions to meet specific needs"}],imageSrc:"/img/portfolio/thumbnails/aiflow_notes.png",imageAlt:"Overview of the all-in-one AI assistant features and customizable prompts"}].map(((e,s)=>(0,b.jsxs)("div",{className:c.A.featureGrid,style:{marginTop:s>0?"3rem":"0",flexDirection:s%2==0?"row-reverse":"row"},children:[(0,b.jsx)("div",{style:{cursor:"pointer",flex:5},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i(e.imageSrc.replace("thumbnails","fullsize")),id:`aiflow-${s+1}`,alt:e.imageAlt,src:e.imageSrc})}),(0,b.jsxs)("div",{className:c.A.featureContent,style:{flex:3},children:[e.icon&&(0,b.jsx)("div",{className:c.A.benefitIcon,children:e.icon}),(0,b.jsx)(r.A,{as:"h3",children:(0,b.jsx)(l.A,{id:e.nameId,children:e.nameId})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:e.descriptionId,children:e.descriptionId})}),(0,b.jsx)("ul",{className:c.A.featureList,children:e.listItems.map(((e,i)=>(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:e.id,children:e.text})},i)))})]})]},s)))]})})}function _(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"ai-powered-brainstorming",className:c.A.featureSection,children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.title",children:"AI-Powered Brainstorming"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.subtitle",children:"Boost creativity with AI-assisted classic thinking models"})}),(0,b.jsxs)("div",{className:c.A.featureGrid,children:[(0,b.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_productivity.png"),id:"aiflow-brainstorming",alt:"AI-powered brainstorming with classic thinking models",src:"/img/portfolio/thumbnails/aiflow_productivity.png"})}),(0,b.jsxs)("div",{className:c.A.featureContent,style:{flex:2},children:[(0,b.jsx)(r.A,{as:"h3",children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.title",children:"Ideation with Classic Thinking Models"})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.subtitle",children:"AI-powered brainstorming with structured frameworks"})}),(0,b.jsxs)("ul",{className:c.A.featureList,children:[(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.m1",children:"Six Thinking Hats"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.m2",children:"SWOT Analysis"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.m3",children:"McKinsey Method"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.classic_models.m4",children:"First Principles"})})]}),(0,b.jsx)(r.A,{as:"h3",style:{paddingTop:"10px"},children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.title",children:"Ideation with Classic Thinking Models"})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.subtitle",children:"AI-powered brainstorming with structured frameworks"})}),(0,b.jsxs)("ul",{className:c.A.featureList,children:[(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.m1",children:"Presentation Slides"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.m2",children:"Solution Document"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.m3",children:"Infographics"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.ai-powered-brainstorming.oneclick_generation.m4",children:"Images"})})]})]})]})]})})}function y(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"book-insights",className:c.A.featureSection,style:{backgroundColor:"cornsilk"},children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.title",children:"Deep Learning from Books & Movies with AI Mind Maps"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.subtitle",children:"Transform Complex Works into Clear, Actionable Insights"})}),(0,b.jsxs)("div",{className:c.A.featureGrid,children:[(0,b.jsxs)("div",{className:c.A.featureContent,style:{flex:2},children:[(0,b.jsx)("i",{className:"fas fa-4x fa-book-reader text-primary mb-4"}),(0,b.jsx)(r.A,{as:"h3",children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.title",children:"Unlock Knowledge Efficiently"})}),(0,b.jsxs)("ul",{className:c.A.featureList,children:[(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.li1",children:"Extract key concepts and themes instantly"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.li2",children:"Visualize complex relationships and character dynamics"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.li3",children:"Generate comprehensive chapter summaries"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.li4",children:"Explore related topics and deeper insights"})}),(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:"aiflow.book-insights.features.li5",children:"Create personalized learning paths"})})]})]}),(0,b.jsx)("div",{style:{cursor:"pointer",flex:3},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_book.png"),id:"aiflow-brainstorming",alt:"AI-powered mind mapping for book analysis and learning",src:"/img/portfolio/thumbnails/aiflow_book.png"})})]})]})})}function N(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"use-cases",className:c.A.useCases,children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.use-cases.title",children:"Use Cases"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.use-cases.description",children:"AIFlow adapts to diverse knowledge work scenarios, enhancing your thinking process from exploration to execution"})}),(0,b.jsxs)("div",{className:c.A.featureGrid,style:{flexDirection:"row"},children:[(0,b.jsx)("div",{className:c.A.featureContent,style:{flex:3},children:[{icon:"\ud83d\udcda",titleId:"aiflow.use-cases.case1.title",descriptionId:"aiflow.use-cases.case1.description"},{icon:"\ud83c\udfaf",titleId:"aiflow.use-cases.case2.title",descriptionId:"aiflow.use-cases.case2.description"},{icon:"\ud83d\udd0d",titleId:"aiflow.use-cases.case3.title",descriptionId:"aiflow.use-cases.case3.description"},{icon:"\u2699\ufe0f",titleId:"aiflow.use-cases.case4.title",descriptionId:"aiflow.use-cases.case4.description"},{icon:"\ud83d\udcdd",titleId:"aiflow.use-cases.case5.title",descriptionId:"aiflow.use-cases.case5.description"},{icon:"\u26a1",titleId:"aiflow.use-cases.case6.title",descriptionId:"aiflow.use-cases.case6.description"}].map(((e,i)=>(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{style:{fontSize:20,fontWeight:"bold"},children:(0,b.jsx)(l.A,{id:e.titleId,children:"Multidimensional Thinking"})}),(0,b.jsx)("p",{style:{marginBottom:5},children:(0,b.jsx)(l.A,{id:e.descriptionId,children:"Analyze problems comprehensively with multidimensional mind maps"})})]},i)))}),(0,b.jsx)("div",{style:{cursor:"pointer",flex:5},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_learning.png"),alt:"AIFlow boost creativity and productivity under multi scenarios",src:"/img/portfolio/thumbnails/aiflow_learning.png"})})]})]})})}function C(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"thinking-methods",className:c.A.featureSection,style:{backgroundColor:"lavender"},children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.thinking-methods.title",children:"Enhance Your Thinking with AIFlow"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.thinking-methods.description",children:"FunBlocks AIFlow integrates powerful thinking methodologies with AI to enhance your cognitive abilities"})}),[{name:"Brainstorming",description:"Generate a wide range of ideas without judgment",aiFlowFeatures:["AI-assisted idea generation","Classic thinking models integration","Visual organization of ideas","One-click expansion of concepts"],imageSrc:"/img/portfolio/fullsize/aiflow_brainstorming_prompt.png",imageAlt:"AI-powered brainstorming with FunBlocks AIFlow"},{name:"Mind Mapping",description:"Organize information visually to see connections",aiFlowFeatures:["Infinite canvas for complex maps","Hierarchical node structure","AI-generated connections","Visual customization options"],imageSrc:"/img/portfolio/thumbnails/aiflow_book.png",imageAlt:"Mind mapping capabilities in FunBlocks AIFlow"},{name:"Critical Thinking",description:"Analyze information objectively to form reasoned judgments",aiFlowFeatures:["Cognitive bias identification","Assumption testing tools","Multiple perspective analysis","Structured evaluation frameworks"],imageSrc:"/img/portfolio/thumbnails/aiflow_critical_tools.png",imageAlt:"Critical thinking tools in FunBlocks AIFlow"},{name:"Creative Thinking",description:"Develop innovative solutions and unique perspectives",aiFlowFeatures:["Lateral thinking prompts","Analogical reasoning tools","Constraint removal exercises","Idea combination techniques"],imageSrc:"/img/portfolio/thumbnails/aiflow_productivity.png",imageAlt:"Creative thinking enhancement with FunBlocks AIFlow"}].map(((e,s)=>(0,b.jsxs)("div",{className:c.A.featureGrid,style:{marginTop:s>0?"3rem":"0",flexDirection:s%2==0?"row":"row-reverse"},children:[(0,b.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i(e.imageSrc.replace("thumbnails","fullsize")),alt:e.imageAlt,src:e.imageSrc})}),(0,b.jsxs)("div",{className:c.A.featureContent,style:{flex:3},children:[(0,b.jsx)(r.A,{as:"h3",children:(0,b.jsx)(l.A,{id:`aiflow.thinking-methods.${e.name.toLowerCase().replace(" ","_")}.title`,children:e.name})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:`aiflow.thinking-methods.${e.name.toLowerCase().replace(" ","_")}.description`,children:e.description})}),(0,b.jsx)(r.A,{as:"h4",style:{fontSize:"1.1rem",marginTop:"1rem"},children:(0,b.jsx)(l.A,{id:"aiflow.thinking-methods.aiflow-features",children:"AIFlow Features:"})}),(0,b.jsx)("ul",{className:c.A.featureList,children:e.aiFlowFeatures.map(((i,s)=>(0,b.jsx)("li",{children:(0,b.jsx)(l.A,{id:`aiflow.thinking-methods.${e.name.toLowerCase().replace(" ","_")}.feature${s+1}`,children:i})},s)))})]})]},s)))]})})}function S(e){let{setShowImageSrc:i}=e;return(0,b.jsx)("section",{id:"case-studies",className:c.A.featureSection,style:{backgroundColor:"#FFF8DC"},children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.title",children:"AIFlow in Action: Real-World Applications"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.description",children:"See how professionals and students use FunBlocks AIFlow to enhance their thinking and productivity"})}),(0,b.jsxs)("div",{className:c.A.featureGrid,children:[(0,b.jsx)("div",{style:{cursor:"pointer",flex:2},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aitools_brainstorming_marketing.png"),alt:"Case study of AIFlow being used for marketing project planning",src:"/img/portfolio/thumbnails/aitools_brainstorming_marketing.png"})}),(0,b.jsxs)("div",{className:c.A.featureContent,style:{flex:3,maxWidth:"60%"},children:[(0,b.jsx)(r.A,{as:"h3",children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.education.title",children:"Educational Excellence"})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.education.description",children:"A university professor used AIFlow to help students visualize complex concepts in cognitive psychology. Students created mind maps to connect theories, research findings, and real-world applications, resulting in 40% better comprehension compared to traditional note-taking."})}),(0,b.jsx)(r.A,{as:"h3",style:{marginTop:"1.5rem"},children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.business.title",children:"Business Innovation"})}),(0,b.jsx)("p",{children:(0,b.jsx)(l.A,{id:"aiflow.case-studies.business.description",children:"A product development team at a tech startup used AIFlow's brainstorming and critical thinking tools to identify market gaps and develop innovative solutions. The visual approach helped them reduce planning time by 60% while generating 3x more viable product ideas."})})]})]})]})})}function T(){return(0,b.jsx)("section",{id:"educational-resources",className:c.A.featureSection,style:{backgroundColor:"#F0F8FF"},children:(0,b.jsxs)("div",{className:"container",children:[(0,b.jsx)(r.A,{as:"h2",className:c.A.sectionTitle,children:(0,b.jsx)(l.A,{id:"aiflow.educational-resources.title",children:"Educational Resources"})}),(0,b.jsx)("p",{className:c.A.sectionDescription,children:(0,b.jsx)(l.A,{id:"aiflow.educational-resources.description",children:"Enhance your thinking skills with our comprehensive guides and tutorials"})}),(0,b.jsx)("div",{className:c.A.resourcesGrid,children:[{title:"Brainstorming Techniques",description:"Learn effective brainstorming methods enhanced by AI",link:"/docs/aiflow-tricks-and-tips/Brainstorming",icon:"\u26a1"},{title:"Mind Mapping Mastery",description:"Discover how to create powerful mind maps for any purpose",link:"/thinking-matters/intro/mind-mapping",icon:"\ud83d\uddfa\ufe0f"},{title:"Critical Thinking Skills",description:"Enhance your analytical abilities with structured approaches",link:"/docs/aiflow-tricks-and-tips/Critical-Thinking",icon:"\ud83d\udd0d"},{title:"Creative Problem Solving",description:"Break through creative blocks with innovative techniques",link:"/thinking-matters/intro/creative-thinking",icon:"\ud83d\udca1"},{title:"Mental Models Library",description:"Access powerful thinking frameworks for complex problems",link:"/thinking-matters/intro/mental-models",icon:"\ud83e\udde9"},{title:"Integrated Workflow Guide",description:"See how all thinking methods work together in AIFlow",link:"/thinking-matters/intro/funblocks-aiflow-in-action-integrated-workflow-from-problem-to-solution",icon:"\u2699\ufe0f"}].map(((e,i)=>(0,b.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"10px",padding:"1.5rem",boxShadow:"0 4px 6px rgba(0,0,0,0.1)",transition:"transform 0.3s, box-shadow 0.3s",height:"100%",display:"flex",flexDirection:"column"},className:c.A.resourceCard,children:[(0,b.jsx)("div",{style:{fontSize:"2rem",marginBottom:"1rem"},children:e.icon}),(0,b.jsx)(r.A,{as:"h3",style:{fontSize:"1.3rem",marginBottom:"0.5rem"},children:(0,b.jsx)(l.A,{id:`aiflow.educational-resources.resource${i+1}.title`,children:e.title})}),(0,b.jsx)("p",{style:{flex:1},children:(0,b.jsx)(l.A,{id:`aiflow.educational-resources.resource${i+1}.description`,children:e.description})}),(0,b.jsxs)(a.A,{to:e.link,className:c.A.resourceLink,style:{display:"inline-block",marginTop:"1rem",color:"var(--primary)",fontWeight:"bold"},children:[(0,b.jsx)(l.A,{id:"aiflow.educational-resources.learn_more",children:"Learn More"})," \u2192"]})]},i)))})]})})}function F(){const[e,i]=(0,n.useState)(null);function s(){!function(e){window.open().location.href=e}(`https://app.${window.location.hostname.includes("funblocks")?window.location.hostname.replace("www.",""):"funblocks.net"}/#/login?source=flow`)}const t=[{feature:(0,l.T)({id:"aiflow.comparison.feature1",message:"Brainstorming Capabilities"}),funblocks:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),chatgpt:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"}),miro:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"}),notion:"\u274c"},{feature:(0,l.T)({id:"aiflow.comparison.feature2",message:"Mind Mapping"}),funblocks:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),chatgpt:"\u274c",miro:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"}),notion:"\u274c"},{feature:(0,l.T)({id:"aiflow.comparison.feature3",message:"Critical Thinking Tools"}),funblocks:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),chatgpt:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"}),miro:"\u274c",notion:"\u274c"},{feature:(0,l.T)({id:"aiflow.comparison.feature4",message:"Creative Thinking Frameworks"}),funblocks:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),chatgpt:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"}),miro:"\u274c",notion:"\u274c"},{feature:(0,l.T)({id:"aiflow.comparison.feature5",message:"Visual Thinking Integration"}),funblocks:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),chatgpt:"\u274c",miro:(0,l.T)({id:"aiflow.comparison.advanced",message:"\u2705 Advanced"}),notion:(0,l.T)({id:"aiflow.comparison.basic",message:"\u2705 Basic"})}];return(0,b.jsxs)(o.A,{title:(0,l.T)({id:"aiflow.head.title",message:"Visualized Chat with AI, Best for Brainstorming, Mind Mapping, Critical & Creative Thinking"}),description:(0,l.T)({id:"aiflow.head.description",message:"FunBlocks AIFlow: Enhance your thinking with AI-powered brainstorming, mind mapping, critical thinking, and creative thinking tools. Visualize ideas, solve problems & learn faster with GPT-4 & Claude LLM. Free trial available!"}),children:[(0,b.jsx)(j.A,{}),(0,b.jsx)(v,{setShowImageSrc:i,toApp:s}),(0,b.jsxs)("main",{children:[(0,b.jsx)(f.A,{page:"aiflow",feature:"intro",pointNos:[1,2,3,4],style:{backgroundColor:"lightcyan"},imageElement:(0,b.jsx)("div",{style:{flex:4,cursor:"pointer"},children:(0,b.jsx)("img",{className:c.A.featureImage,alt:"FunBlocks AIFlow benefits compared to ChatGPT",src:"/img/portfolio/thumbnails/aiflow_benefits.png",onClick:()=>i("/img/portfolio/fullsize/aiflow_benefits.png")})})}),(0,b.jsx)(C,{setShowImageSrc:i}),(0,b.jsx)(I,{setShowImageSrc:i}),(0,b.jsx)(_,{setShowImageSrc:i}),(0,b.jsx)(y,{setShowImageSrc:i}),(0,b.jsx)(f.A,{page:"aiflow",feature:"explore-with-ai",pointNos:[1,2,3,4],style:{backgroundColor:"#F0FFF0"},imageElement:(0,b.jsx)("div",{style:{cursor:"pointer",flex:3.5},children:(0,b.jsx)("img",{className:c.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_related_question.png"),alt:"AI-guided topic discovery and exploration with AIFlow",src:"/img/portfolio/thumbnails/aiflow_related_question.png"})})}),(0,b.jsx)(A.A,{page:"aiflow",customData:t,titleTranslateId:"aiflow.comparison.title",descriptionTranslateId:"aiflow.comparison.description",competitors:{funblocks:{label:"FunBlocks AIFlow",isHighlighted:!0},chatgpt:{label:"ChatGPT",isHighlighted:!1},miro:{label:"Miro",isHighlighted:!1},notion:{label:"Notion",isHighlighted:!1}}}),(0,b.jsx)(S,{setShowImageSrc:i}),(0,b.jsx)(N,{setShowImageSrc:i}),(0,b.jsx)(m.A,{}),(0,b.jsx)(T,{}),(0,b.jsx)(k.A,{page:"aiflow",showProductHuntBadges:!0}),(0,b.jsx)(p.A,{avatars:["\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc69\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udf93","\ud83d\udc69\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83c\udfeb"],page:"aiflow"}),(0,b.jsx)(w.A,{}),(0,b.jsx)(x.A,{toApp:s,page:"aiflow"}),(0,b.jsx)(h.A,{page:"aiflow",faqIds:["q1","q2","q3","q4","q5","q6","q7","q8","q9","q10","q11","q12"]})]}),(0,b.jsx)(d.A,{}),e&&(0,b.jsx)(g.A,{imageSrc:e,setImageSrc:i}),(0,b.jsx)(u.A,{page:"aiflow"})]})}},63989:(e,i,s)=>{s.d(i,{A:()=>o});s(96540);var n=s(68154),t=s(40797),a=s(74848);const o=function(){const{siteConfig:e}=(0,t.A)(),{title:i,tagline:s,url:o}=e,l={"@context":"https://schema.org","@type":"Organization",name:"FunBlocks AI",url:o,logo:`${o}/img/logo.png`,sameAs:["https://twitter.com/funblocksai","https://www.linkedin.com/company/funblocksai"],description:s};return(0,a.jsxs)(n.m,{children:[(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify(l)}),(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AIFlow",applicationCategory:"ProductivityApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"An AI-driven platform that enhances thinking and productivity through visualization. Convert intricate concepts into engaging visual mind maps, dynamic slides, and informative infographics.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"2030"}})}),(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AIFlow?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AIFlow is an AI-driven platform that enhances thinking and productivity through visualization. It helps you convert intricate concepts into engaging visual mind maps, dynamic slides, and informative infographics."}},{"@type":"Question",name:"Which AI models does FunBlocks support?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI supports all mainstream large language models including OpenAI GPT, Anthropic Claude, Google Gemini, and DeepSeek, giving you the freedom to choose the best AI for your specific needs."}},{"@type":"Question",name:"How does FunBlocks AIFlow differ from traditional AI chatbots?",acceptedAnswer:{"@type":"Answer",text:"Unlike traditional AI chatbots that limit thinking to linear conversations, FunBlocks AIFlow expands your cognitive horizon with multidimensional mind maps on an infinite canvas, allowing you to explore topics from multiple angles simultaneously."}}]})})]})}},78905:(e,i,s)=>{s.d(i,{A:()=>m});s(96540);var n=s(34164),t=s(50539);const a="btn_4iM2",o="ctaButtons_Cfhe",l="ctaBtn_Hq_p",r="ctaSection_vQl5";var c=s(9303),d=s(56289),h=s(74848);const m=function(e){let{page:i,toUrl:s,toApp:m,customButtonText:p}=e;return(0,h.jsx)("section",{id:"cta",className:r,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(c.A,{as:"h2",children:(0,h.jsx)(t.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(t.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:o,children:(0,h.jsx)(d.A,{className:(0,n.A)(a,l),to:s,onClick:s?void 0:()=>m(),children:p||(0,h.jsx)(t.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,s)=>{s.d(i,{A:()=>c});s(96540);var n=s(50539);const t="modal_osiT",a="modalImage_HWh8",o="close_Y6T6",l="zoomIndicator_r4Py";var r=s(74848);const c=function(e){let{imageSrc:i,setImageSrc:s}=e;const c=()=>{s(null)};return(0,r.jsxs)("div",{className:t,style:{display:"flex"},onClick:c,children:[(0,r.jsx)("span",{className:o,onClick:c,children:"\xd7"}),(0,r.jsx)("img",{className:a,src:i,alt:(0,n.T)({id:"modal.alt",message:"Enlarged view"})}),(0,r.jsx)("div",{className:l,children:(0,r.jsx)(n.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,s)=>{s.d(i,{A:()=>t});s(96540);var n=s(74848);const t=function(e){let{page:i}=e;const s=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",t=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${s?"source="+s+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:t}})})}},83890:(e,i,s)=>{s.d(i,{A:()=>j});s(96540);var n=s(34164),t=s(50539);const a="btnSecondary_I9vp",o="btnSm_efuA",l="ctaButtons_ngQW",r="sectionTitle_TGU3",c="sectionDescription_wsiT",d="toolsSection__Rt2",h="fullWidthImage_zV7g",m="toolsList_XeMD",p="toolItem_KeCk",g="toolTitle_y9e3",u="toolDescription_GKIq";var f=s(9303),x=s(56289),w=s(74848);const j=function(){return(0,w.jsx)("section",{id:"tools",className:d,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(f.A,{as:"h2",className:r,style:{marginBottom:"1rem"},children:(0,w.jsx)(t.A,{id:"homepage.tools.title",children:"Quick Access AI Tools"})}),(0,w.jsx)("p",{className:c,children:(0,w.jsx)(t.A,{id:"homepage.tools.description",children:"FunBlocks AIFlow is a comprehensive creativity and productivity platform with specialized tools designed for specific cognitive challenges."})}),(0,w.jsx)("img",{id:"aitools",alt:"FunBlocks AI Tools",className:h,src:"/img/portfolio/thumbnails/ai_tools.png"}),(0,w.jsxs)("div",{className:m,children:[(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool1.title",children:"AI Mind Map Generator"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool1.description",children:"Easily convert documents, books, and movies into mind maps to help you clarify complex ideas quickly."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool2.title",children:"AI Brainstorming"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool2.description",children:"Combine AI with classic thinking models to brainstorm on specific topics or problems, sparking creativity and innovative solutions."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool3.title",children:"AI Critical Thinking"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool3.description",children:"Enhance your critical thinking skills, gaining insights from multiple perspectives through questioning, analysis, and reflection."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool4.title",children:"AI Slides Generator"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool4.description",children:"Quickly create professional slides and presentations on any topic, making it easy to prepare for your presentations."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool5.title",children:"AI Infographics Generator"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool5.description",children:"Automatically generate infographics and knowledge cards from input text, helping you convey information visually."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool6.title",children:"AI Art Insight"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool6.description",children:"Gain deeper artistic analysis and appreciation by taking photos during your travels and museum visits."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool7.title",children:"AI Education Tool"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool7.description",children:"Break down topics into progressive cognitive levels based on Bloom's educational theories to enhance learning efficiency and teaching effectiveness."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool8.title",children:"AI Psychological Insights"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool8.description",children:"Receive psychological counseling and dream interpretation services anytime, helping you better understand your inner world."})})]}),(0,w.jsxs)("div",{className:p,children:[(0,w.jsx)("div",{className:g,children:(0,w.jsx)("span",{children:(0,w.jsx)(t.A,{id:"homepage.tools.tool9.title",children:"AI Image Generator"})})}),(0,w.jsx)("p",{className:u,children:(0,w.jsx)(t.A,{id:"homepage.tools.tool9.description",children:"Generate personalized avatars and images in your desired style with a single click."})})]})]}),(0,w.jsx)("div",{className:l,children:(0,w.jsx)(x.A,{className:(0,n.A)("button",a,o),to:"https://www.funblocks.net/aitools",target:"_blank",children:(0,w.jsx)(t.A,{id:"homepage.tools.tools_list",children:"AI Tools"})})})]})})}},87263:(e,i,s)=>{s.d(i,{A:()=>f});var n=s(96540),t=s(34164),a=s(50539);const o="sectionTitle_gwu3",l="faqSection_DBlu",r="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",p="faqAnswer_HbCX";var g=s(74848);function u(e){let{page:i,questionId:s,answerId:o}=e;const[l,r]=(0,n.useState)(!1);return(0,g.jsxs)("div",{className:(0,t.A)(c,{[m]:l}),children:[(0,g.jsxs)("div",{className:d,onClick:()=>{r(!l)},children:[(0,g.jsx)("span",{style:{fontWeight:"normal"},children:(0,g.jsx)(a.A,{id:`${i}.faq.${s}`})}),(0,g.jsx)("div",{className:h,style:{transform:l?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,g.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:l?"block":"none"},children:(0,g.jsx)(a.A,{id:`${i}.faq.${o}`})})]})}const f=function(e){let{page:i,faqIds:s}=e;return(0,g.jsx)("section",{id:"faqs",className:(0,t.A)("page-section",l),style:{backgroundColor:"var(--gray)"},children:(0,g.jsxs)("div",{className:"container",children:[(0,g.jsx)("h2",{className:o,children:(0,g.jsx)(a.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,g.jsx)("div",{className:r,children:s.map((e=>(0,g.jsx)(u,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);