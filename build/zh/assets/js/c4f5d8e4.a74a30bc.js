"use strict";(self.webpackChunkfunblocks_docs=self.webpackChunkfunblocks_docs||[]).push([[52634],{18044:(e,i,s)=>{s.d(i,{A:()=>k});s(96540);var n=s(50539);const t="socialProofSection_iQYk",a="sectionTitle_K_Fi",o="metricsContainer_WuZH",l="metricCard_3RKd",r="metricIcon_NIST",c="metricValue_qazu",d="metricLabel_Vwzg",h="companiesContainer_Rn5K",m="companiesTitle_lfNe",p="companyGrid_WPY0",u="productHuntContainer_aglb",g="badgesTitle_pxwV",x="badgesWrapper_OcIX",j="badgeLink_snWB",f="badgeImage_TCK6",A="companyItem_kt31";var v=s(9303),w=s(74848);const k=function(e){let{page:i="homepage",customCompanies:s=null,customMetrics:k=null,titleTranslateId:b=null,companiesTitleTranslateId:I=null,showProductHuntBadges:_=!0}=e;const y=[{value:"100,000+",label:(0,w.jsx)(n.A,{id:`${i}.socialProof.users`,children:"Active Users"}),icon:"\ud83d\udc65"},{value:"60%",label:(0,w.jsx)(n.A,{id:`${i}.socialProof.productivity`,children:"Productivity Increase"}),icon:"\ud83d\udcc8"},{value:"80+",label:(0,w.jsx)(n.A,{id:`${i}.socialProof.countries`,children:"Countries"}),icon:"\ud83c\udf0e"},{value:"4.8/5",label:(0,w.jsx)(n.A,{id:`${i}.socialProof.rating`,children:"User Rating"}),icon:"\u2b50"}],N=s||["Google","Amazon","Microsoft","ByteDance","Tencent","XiaoMi","MIT","IBM","Meta","Harvard University","Stanford University","Yale University"],C=k||y;return(0,w.jsx)("section",{id:"social-proof",className:t,children:(0,w.jsxs)("div",{className:"container",children:[(0,w.jsx)(v.A,{as:"h2",className:a,children:(0,w.jsx)(n.A,{id:b||`${i}.socialProof.title`,children:"Trusted by Professionals Worldwide"})}),_&&(0,w.jsxs)("div",{className:u,children:[(0,w.jsx)("p",{className:g,children:(0,w.jsx)(n.A,{id:`${i}.socialProof.productHunt`,children:"Featured on Product Hunt"})}),(0,w.jsxs)("div",{className:x,children:[(0,w.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:j,children:(0,w.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=486382&theme=dark&period=daily&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:f})}),(0,w.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-aiflow?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_souce=badge-funblocks-aiflow",target:"_blank",className:j,children:(0,w.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=486382&theme=dark&period=weekly&topic_id=204&t=1746785584195",alt:"FunBlocks AIFlow - An AI-powered whiteboard and mind map tool | Product Hunt",className:f})})]})]}),(0,w.jsx)("div",{className:o,children:C.map(((e,i)=>(0,w.jsxs)("div",{className:l,children:[(0,w.jsx)("div",{className:r,children:e.icon}),(0,w.jsx)("div",{className:c,children:e.value}),(0,w.jsx)("div",{className:d,children:e.label})]},i)))}),(0,w.jsxs)("div",{className:h,children:[(0,w.jsx)("p",{className:m,children:(0,w.jsx)(n.A,{id:I||`${i}.socialProof.companies`,children:"Used by innovative teams at"})}),(0,w.jsx)("div",{className:p,children:N.map(((e,i)=>(0,w.jsx)("div",{className:A,children:e},i)))})]})]})})}},21099:(e,i,s)=>{s.d(i,{A:()=>n});const n={mainNav:"mainNav_wvJd",headerContainer:"headerContainer_Dcc3",logo:"logo_Ukns",navLinks:"navLinks_FO3Z",languageSelector:"languageSelector_q2Kz",hero:"hero_aEcG",heroContent:"heroContent_mKPX",heroSubtitle:"heroSubtitle_jFu1",heroButtons:"heroButtons_r52D",heroImage:"heroImage_xZN7",btn:"btn_bvfa",btnSecondary:"btnSecondary_mRVh",btnSm:"btnSm_WyTc",beyondChatgpt:"beyondChatgpt_vcba",sectionTitle:"sectionTitle_Ut5p",sectionDescription:"sectionDescription_cpL1",twoColumnGrid:"twoColumnGrid_m4Cd",benefitsContainer:"benefitsContainer_XC0u",benefitCard:"benefitCard_IkhP",cardTitle:"cardTitle_tke3",benefitIcon:"benefitIcon_Td8l",toolsSection:"toolsSection_lLH3",featureSection:"featureSection_fSH9",featureGrid:"featureGrid_hfN5",featureContent:"featureContent_dLOY",featureList:"featureList_i_0T",featureImage:"featureImage_wMIZ",resourceCard:"resourceCard_Yk8o",resourceLink:"resourceLink__Fuw",thinkingMethodsContainer:"thinkingMethodsContainer_Wadn",thinkingMethodItem:"thinkingMethodItem_ZoxO",thinkingMethodIcon:"thinkingMethodIcon_OcrP",thinkingMethodText:"thinkingMethodText_VqaF",resourcesGrid:"resourcesGrid_WS1N",docsFeatureImage:"docsFeatureImage_y0Cm",fullWidthImage:"fullWidthImage_EopA",multiModelAdvantage:"multiModelAdvantage_rk6v",modelLogosContainer:"modelLogosContainer_cX68",modelLogoItem:"modelLogoItem_OBoq",modelLogo:"modelLogo_Bo1Q",modelName:"modelName_tSDi",advantageText:"advantageText_YvCb",useCases:"useCases_G4kv",useCasesGrid:"useCasesGrid_PM67",useCaseCard:"useCaseCard_t5pd",useCaseIcon:"useCaseIcon_Ea7a",workspaceSection:"workspaceSection_mjbP",ctaButtons:"ctaButtons_vsp7",ctaBtn:"ctaBtn_gk09",toolsList:"toolsList_ralw",pageSection:"pageSection_REEF",slidesHeader:"slidesHeader_ze7v",slidesContainer:"slidesContainer_GkCC",slidesTitle:"slidesTitle_pfQd",slidesSubtitle:"slidesSubtitle__hsE",slidesTarget:"slidesTarget_meJo",slidesFeatureSection:"slidesFeatureSection_zXW1",slidesAISection:"slidesAISection_kcLU",slidesFeatureIcon:"slidesFeatureIcon_wZVZ",slidesCardContent:"slidesCardContent_jd0w",slidesRow:"slidesRow_hH1c",slidesCol4:"slidesCol4_wnUj",slidesCol8:"slidesCol8_jM8j",imageLeft:"imageLeft_EIxX",imageRight:"imageRight_hkp1",centerContainer:"centerContainer_QTal",order1:"order1_XamF",order2:"order2_fOta"}},26167:(e,i,s)=>{s.d(i,{A:()=>c});const n="footer_m3PR",t="footerContainer_g8s3",a="footerLinks_EjWI",o="toolsGrid_N_gp",l="copyright_zlJy";var r=s(74848);const c=function(){return(0,r.jsx)("footer",{className:n,children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:t,children:[(0,r.jsxs)("div",{className:a,style:{marginRight:"20px"},children:[(0,r.jsx)("span",{className:"footer-logo",children:"FunBlocks"}),(0,r.jsx)("p",{"data-i18n":"footer.description",style:{color:"#bbb"},children:"An AI-powered platform for visualization-enhanced thinking and productivity."})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.product",children:"FunBlocks AI Products"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aiflow",children:"FunBlocks AI Flow"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aitools",children:"FunBlocks AI Tools"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/welcome_extension",children:"FunBlocks AI Extension"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/slides",children:"FunBlocks AI Slides"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/aidocs",children:"FunBlocks AI Docs"})})]})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.resources",children:"Resources"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/docs",children:"FunBlocks AI Tutorials"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/blog",children:"FunBlocks AI Blog"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://app.funblocks.net/shares",children:"FunBlocks AI Generated Content"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Reading",children:"Classic Book Mindmaps"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/collections/Movie",children:"Classic Movie Mindmaps"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/thinking-matters/behind-aiflow",children:"Thinking Matters"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/thinking-matters/category/classic-mental-models",children:"Mental Models"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.funblocks.net/ai101",children:"AI Basics: AI 101"})})]})]}),(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.company",children:"Company"}),(0,r.jsx)("ul",{children:(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://discord.gg/XtdZFBy4uR",target:"_blank",children:"Contact Us"})})})]})]}),(0,r.jsx)("div",{className:t,children:(0,r.jsxs)("div",{className:a,children:[(0,r.jsx)("h4",{"data-i18n":"footer.resources",children:"FunBlocks AI Tools"}),(0,r.jsxs)("div",{className:o,children:[(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindmap",target:"_blank",children:"AI Mindmap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/slides",target:"_blank",children:"AI Slides"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/graphics",target:"_blank",children:"AI Graphics"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/brainstorming",target:"_blank",children:"AI Brainstorming"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindkit",target:"_blank",children:"AI MindKit"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/youtube",target:"_blank",children:"AI Youtube Summarizer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/critical-thinking",target:"_blank",children:"AI Critical Analysis"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/refine-question",target:"_blank",children:"AI Question Craft"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/bias",target:"_blank",children:"AI LogicLens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/reflection",target:"_blank",children:"AI Reflection"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/decision",target:"_blank",children:"AI Decision Analyzer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/okr",target:"_blank",children:"AI OKR Assistant"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/startupmentor",target:"_blank",children:"AI Startup Mentor"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/businessmodel",target:"_blank",children:"AI Business Model Analyzer"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/planner",target:"_blank",children:"AI Task Planner"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/counselor",target:"_blank",children:"AI Counselor"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/dreamlens",target:"_blank",children:"AI DreamLens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/horoscope",target:"_blank",children:"AI Horoscope"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/art",target:"_blank",children:"AI Art Insight"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/photo",target:"_blank",children:"AI Photo Coach"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/poetic",target:"_blank",children:"AI Poetic Lens"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/avatar",target:"_blank",children:"AI Avatar Studio"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/erase",target:"_blank",children:"AI Watermarks Remover"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/reading",target:"_blank",children:"AI Reading Map"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/movie",target:"_blank",children:"AI CineMap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/feynman",target:"_blank",children:"AI Feynman"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/marzano",target:"_blank",children:"AI Marzano Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/bloom",target:"_blank",children:"AI Bloom Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/solo",target:"_blank",children:"AI SOLO Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/dok",target:"_blank",children:"AI DOK Taxonomy"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/layered-explanation",target:"_blank",children:"AI MindLadder"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/infographic",target:"_blank",children:"AI Infographic"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/insightcards",target:"_blank",children:"AI InsightCards"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/mindsnap",target:"_blank",children:"AI MindSnap"}),(0,r.jsx)("a",{href:"https://www.funblocks.net/aitools/one-page-slide",target:"_blank",children:"AI SlideGenius"})]})]})}),(0,r.jsx)("div",{className:l,children:(0,r.jsx)("p",{"data-i18n":"footer.copyright",children:"\xa9 2025 FunBlocks AI. All rights reserved."})})]})})}},40101:(e,i,s)=>{s.d(i,{A:()=>j});s(96540);var n=s(34164),t=s(56289),a=s(50539),o=s(9303);const l="thinkingMattersSection_WOo4",r="sectionTitle_eJhx",c="sectionDescription_pO3x",d="thinkingGridContainer_l_4S",h="thinkingCard_pQuf",m="cardTitle_iexr",p="thinkingIcon_qo4E",u="learnMoreContainer_c0ZI",g="learnMoreButton_BcL4";var x=s(74848);const j=function(e){let{page:i="homepage"}=e;return(0,x.jsx)("section",{id:"thinking-matters",className:(0,n.A)(l),children:(0,x.jsxs)("div",{className:"container",children:[(0,x.jsx)(o.A,{as:"h2",className:r,children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.title`,children:"Your Thinking Matters in the Age of AI"})}),(0,x.jsx)("p",{className:c,children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.description`,children:"In today's fast-paced world filled with AI tools, effective thinking skills are more valuable than ever. FunBlocks empowers you to enhance your cognitive abilities through structured thinking methods and visualization."})}),(0,x.jsxs)("div",{className:d,children:[(0,x.jsxs)("div",{className:h,children:[(0,x.jsxs)("div",{className:m,children:[(0,x.jsx)("div",{className:p,children:"\ud83e\udded"}),(0,x.jsx)(o.A,{as:"h4",children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.master.title`,children:"Master Your Mind"})})]}),(0,x.jsx)("p",{children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.master.description`,children:"In today's information-rich world, effective thinking methods are essential. FunBlocks AIF helps you cut through noise, gain clarity, and boost efficiency by integrating proven cognitive techniques with intuitive technology."})})]}),(0,x.jsxs)("div",{className:h,children:[(0,x.jsxs)("div",{className:m,children:[(0,x.jsx)("div",{className:p,children:"\ud83e\udde9"}),(0,x.jsx)(o.A,{as:"h4",children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.concepts.title`,children:"Core Thinking Concepts"})})]}),(0,x.jsx)("p",{children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.concepts.description`,children:"Our platform is built on fundamental thinking methodologies: Brainstorming for idea generation, Mind Mapping for organization, Critical Thinking for analysis, Creative Thinking for innovation, and Mental Models for understanding."})})]}),(0,x.jsxs)("div",{className:h,children:[(0,x.jsxs)("div",{className:m,children:[(0,x.jsx)("div",{className:p,children:"\ud83c\udf1f"}),(0,x.jsx)(o.A,{as:"h4",children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.empowerment.title`,children:"AI-Enhanced Thinking"})})]}),(0,x.jsx)("p",{children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.empowerment.description`,children:"FunBlocks AIFlow enhances your thinking with a unified workspace, visualization tools, AI assistance, and seamless collaboration features\u2014all designed to help you think more deeply and effectively."})})]})]}),(0,x.jsx)("div",{className:u,children:(0,x.jsx)(t.A,{className:(0,n.A)("button",g),to:"/thinking-matters/behind-aiflow",children:(0,x.jsx)(a.A,{id:`${i}.thinking_matters.learn_more`,children:"Learn More About Our Thinking Toolkit"})})})]})})}},46258:(e,i,s)=>{s.d(i,{A:()=>A});s(96540);var n=s(34164),t=s(50539);const a="comparisonSection_xGN0",o="responsiveContainer_LWkJ",l="sectionTitle_WEFW",r="sectionDescription_C78Y",c="tableContainer_XXdk",d="comparisonTable_nUFG",h="featureHeader_MZvb",m="featureCell_N7ZK",p="funblocksHeader_W9br",u="funblocksCell_mVK7",g="comparisonNote_BsSN",x="scrollIndicator_qOFX";var j=s(9303),f=s(74848);const A=function(e){let{page:i="homepage",customData:s=null,titleTranslateId:A=null,descriptionTranslateId:v=null,noteTranslateId:w=null,competitors:k=null}=e;const b={funblocks:{label:(0,f.jsx)(t.A,{id:`${i}.comparison.funblocksHeader`,children:"FunBlocks AI"}),isHighlighted:!0},chatbots:{label:(0,f.jsx)(t.A,{id:`${i}.comparison.chatbotsHeader`,children:"AI Chatbots"}),isHighlighted:!1},notion:{label:(0,f.jsx)(t.A,{id:`${i}.comparison.notionHeader`,children:"Notion"}),isHighlighted:!1},mindmap:{label:(0,f.jsx)(t.A,{id:`${i}.comparison.mindmapHeader`,children:"Mind Map Tools"}),isHighlighted:!1}},I=k||b,_=[{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature1`,children:"All-in-One AI Workspace"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature2`,children:"Visual Thinking & Mind Mapping"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!0},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature3`,children:"AI-Powered Documents"}),funblocks:!0,chatbots:!1,notion:!0,mindmap:!1},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature4`,children:"AI Slide Generation"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature5`,children:"Infographic Creation"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:!1},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature6`,children:"Multi-Model AI Support"}),funblocks:!0,chatbots:!1,notion:!1,mindmap:!1},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature7`,children:"Thinking Frameworks"}),funblocks:!0,chatbots:"Limited",notion:!1,mindmap:"Limited"},{feature:(0,f.jsx)(t.A,{id:`${i}.comparison.feature8`,children:"Seamless Integration Between Tools"}),funblocks:!0,chatbots:!1,notion:"Limited",mindmap:!1}],y=s||_;return(0,f.jsx)("section",{id:"comparison",className:a,children:(0,f.jsxs)("div",{className:(0,n.A)("container",o),children:[(0,f.jsx)(j.A,{as:"h2",className:l,children:(0,f.jsx)(t.A,{id:A||`${i}.comparison.title`,children:"How FunBlocks Compares"})}),(0,f.jsx)("p",{className:r,children:(0,f.jsx)(t.A,{id:v||`${i}.comparison.description`,children:"FunBlocks AI is a complete All-in-One AI Workspace that includes brainstorming, idea generation, visual mind mapping with AIFlow, plus AI-powered docs, slides, and infographics to efficiently complete any type of work"})}),(0,f.jsx)("div",{className:x,children:(0,f.jsx)(t.A,{id:`${i}.comparison.scrollIndicator`,children:"\u2190 Swipe horizontally to see more \u2192"})}),(0,f.jsx)("div",{className:c,children:(0,f.jsxs)("table",{className:d,style:{"--competitor-count":Object.keys(I).length},children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{className:h,children:(0,f.jsx)(t.A,{id:`${i}.comparison.featureHeader`,children:"Feature"})}),Object.entries(I).map((e=>{let[i,s]=e;return(0,f.jsx)("th",{className:s.isHighlighted?p:void 0,children:s.label},i)}))]})}),(0,f.jsx)("tbody",{children:y.map(((e,i)=>(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{className:m,children:e.feature}),Object.entries(I).map((i=>{let[s,n]=i;return(0,f.jsx)("td",{className:n.isHighlighted?u:void 0,children:!0===e[s]?"\u2705":!1===e[s]?"\u274c":e[s]},s)}))]},i)))})]})}),(0,f.jsx)("div",{className:g,children:(0,f.jsx)("p",{children:(0,f.jsx)(t.A,{id:w||`${i}.comparison.note`,children:"FunBlocks AI provides a unified workspace that combines the best of AI chatbots, document tools, and mind mapping software into one seamless platform, eliminating the need to switch between multiple tools and subscriptions."})})})]})})}},51971:(e,i,s)=>{s.d(i,{A:()=>g});var n=s(96540),t=s(50539);const a="sectionTitle_pRDY",o="sectionDescription_GyST",l="benefitsContainer_jm1z",r="testimonialsSection_bcfx",c="testimonialCard_jqt8",d="testimonialHeader_K3A9",h="testimonialAvatar_yvW1",m="testimonialInfo_YZnM";var p=s(9303),u=(s(56289),s(74848));const g=function(e){let{page:i,avatars:s}=e;const g=(0,n.useMemo)((()=>s.map(((e,s)=>({avatar:e,nameId:`${i}.testimonials.user${s+1}.name`,roleId:`${i}.testimonials.user${s+1}.role`,textId:`${i}.testimonials.user${s+1}.text`})))),[i,s]);return(0,u.jsx)("section",{id:"testimonials",className:r,children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)(p.A,{as:"h2",className:a,children:(0,u.jsx)(t.A,{id:"homepage.testimonials.title",children:"What Our Users Say"})}),(0,u.jsx)("p",{className:o,children:(0,u.jsx)(t.A,{id:"homepage.testimonials.description",children:"Discover how FunBlocks AI is transforming the way professionals, students, and teams work, learn, and create."})}),(0,u.jsx)("div",{className:l,children:g?.map(((e,i)=>(0,u.jsxs)("div",{className:c,children:[(0,u.jsxs)("div",{className:d,children:[(0,u.jsx)("div",{className:h,children:(0,u.jsx)("span",{children:e.avatar})}),(0,u.jsxs)("div",{className:m,children:[(0,u.jsx)("h4",{children:(0,u.jsx)(t.A,{id:e.nameId,children:e.nameId})}),(0,u.jsx)("p",{children:(0,u.jsx)(t.A,{id:e.roleId,children:e.roleId})})]})]}),(0,u.jsx)("div",{children:"\u2b50\u2b50\u2b50\u2b50\u2b50"}),(0,u.jsx)("p",{children:(0,u.jsx)(t.A,{id:e.textId,children:e.textId})})]},i)))})]})})}},57456:(e,i,s)=>{s.r(i),s.d(i,{default:()=>Q});var n=s(96540),t=s(34164),a=s(56289),o=s(40797),l=s(30300),r=s(50539),c=s(9303),d=s(21099),h=s(26167),m=s(87263),p=s(83890),u=s(51971),g=s(79912),x=s(81896),j=s(78905),f=s(40101),A=s(63989),v=s(18044),w=s(46258);const k="caseStudiesSection_TbK8",b="sectionTitle_xnli",I="sectionDescription_m6b8",_="caseStudyTabs_BXPR",y="caseStudyTab_J0d0",N="activeTab_sA0h",C="caseIcon_QGPp",S="caseStudyContent_nLgE",T="caseStudyInfo_l4Mk",F="resultsList_vnE_",B="caseStudyImages_jTNJ",$="imageContainer_cG8u",M="caseImage_fxcw";var z=s(74848);const L=function(e){let{setShowImageSrc:i,page:s="homepage",customCases:a=null,titleTranslateId:o=null,descriptionTranslateId:l=null}=e;const d=[{title:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.title`,children:"Educational Institution Transformation"}),description:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.description`,children:"A leading university implemented FunBlocks AI Education Tools (BloomBrain, MarzanoBrain, AIFlow, AI Slides) to enhance teaching materials and develop students' critical thinking skills. The comprehensive suite dramatically improved teaching efficiency and student learning outcomes."}),results:[(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.result1`,children:"65% faster creation of high-quality teaching materials"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.result2`,children:"42% improvement in students' critical thinking abilities"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.result3`,children:"38% increase in creative problem-solving skills"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case1.result4`,children:"Significant enhancement in visual learning comprehension"})],image:"/img/portfolio/fullsize/aitools_infographics_layered.png",icon:"\ud83c\udf93"},{title:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.title`,children:"Corporate Innovation & Productivity"}),description:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.description`,children:"A Fortune 500 company integrated FunBlocks AI across their teams to boost creativity and work efficiency. Teams used AIFlow for brainstorming new product features and marketing campaigns, while leveraging the complete workspace for seamless collaboration and execution."}),results:[(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.result1`,children:"3x increase in innovative product feature ideas"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.result2`,children:"58% reduction in meeting time through visual collaboration"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.result3`,children:"45% better cross-team alignment and communication"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case2.result4`,children:"70% faster creation of professional presentations"})],image:"/img/portfolio/fullsize/aitools_mindsnap_eisenhower_matrix.png",icon:"\ud83d\udcbc"},{title:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.title`,children:"Personal Growth & Learning"}),description:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.description`,children:"Individuals have transformed their lifelong learning and personal development using FunBlocks AI. From creating book mind maps and exploring new topics to generating creative content for social media, users report significant improvements in knowledge retention and creative expression."}),results:[(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.result1`,children:"52% better retention of book concepts with mind maps"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.result2`,children:"4x deeper exploration of interesting topics"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.result3`,children:"68% increase in creative writing output"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case3.result4`,children:"Significant boost in social media engagement"})],image:"/img/portfolio/fullsize/aitools_mindmap_book.png",icon:"\ud83c\udf31"},{title:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.title`,children:"Content Creator Workflow"}),description:(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.description`,children:"Professional content creators have revolutionized their workflow with FunBlocks AI. Beyond faster content production, they now easily create engaging infographics, insight cards, and visual stories that significantly increase audience engagement and differentiate their content."}),results:[(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.result1`,children:"50% faster content production"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.result2`,children:"75% increase in visual content creation"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.result3`,children:"3x higher engagement with infographics and insight cards"}),(0,z.jsx)(r.A,{id:`${s}.caseStudies.case4.result4`,children:"Significant improvement in content uniqueness"})],image:"/img/portfolio/fullsize/aitools_mindsnap_first_principle.png",icon:"\ud83c\udfa8"}],h=a||d,[m,p]=(0,n.useState)(0);return(0,z.jsx)("section",{id:"case-studies",className:k,children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsx)(c.A,{as:"h2",className:b,children:(0,z.jsx)(r.A,{id:o||`${s}.caseStudies.title`,children:"Real-World Success Stories"})}),(0,z.jsx)("p",{className:I,children:(0,z.jsx)(r.A,{id:l||`${s}.caseStudies.description`,children:"See how organizations and individuals are transforming their work with FunBlocks AI's comprehensive suite of tools"})}),(0,z.jsx)("div",{className:_,children:h.map(((e,i)=>(0,z.jsxs)("button",{className:(0,t.A)(y,i===m&&N),onClick:()=>p(i),children:[(0,z.jsx)("span",{className:C,children:e.icon}),(0,z.jsx)("span",{children:e.title})]},i)))}),(0,z.jsxs)("div",{className:S,children:[(0,z.jsxs)("div",{className:T,children:[(0,z.jsx)("h3",{children:h[m].title}),(0,z.jsx)("p",{children:h[m].description}),(0,z.jsxs)("div",{className:F,children:[(0,z.jsx)("h4",{children:(0,z.jsx)(r.A,{id:"homepage.caseStudies.results",children:"Key Results"})}),(0,z.jsx)("ul",{children:h[m].results.map(((e,i)=>(0,z.jsx)("li",{children:e},i)))})]})]}),(0,z.jsx)("div",{className:B,children:(0,z.jsx)("div",{className:$,children:(0,z.jsx)("img",{src:h[m].image,alt:`FunBlocks AI - ${h[m].title}`,className:M,onClick:()=>i&&i(h[m].image),onError:e=>{e.target.onerror=null,e.target.src="/img/placeholder-image.png"}})})})]})]})})};var q=s(74648);function G(e){let{setShowImageSrc:i,toApp:s}=e;const{siteConfig:n}=(0,o.A)();return(0,z.jsx)("section",{id:"hero",className:(0,t.A)(d.A.hero,d.A.pageSection),style:{background:"linear-gradient(135deg,#faf1f8 0%, rgb(81, 111, 242) 100%)"},children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsxs)("div",{className:d.A.heroContent,children:[(0,z.jsx)(c.A,{as:"h1",children:(0,z.jsx)(r.A,{id:"homepage.hero.title",children:"Explore, Think and Create with AI"})}),(0,z.jsx)("p",{className:d.A.heroSubtitle,children:(0,z.jsx)(r.A,{id:"homepage.hero.subtitle",children:"An AI-powered platform that revolutionizes thinking and boosts productivity. Generate ideas with AI, explore topics through mindmaps, and transform them into actionable strategies, engaging presentations, and insightful infographics."})}),(0,z.jsx)("div",{className:d.A.heroButtons,children:(0,z.jsx)(a.A,{className:(0,t.A)("button",d.A.btn),to:"#",onClick:()=>s(),children:(0,z.jsx)(r.A,{id:"homepage.hero.trial",children:"Free Trial Now"})})}),(0,z.jsx)("p",{style:{marginTop:"20px",fontStyle:"italic"},children:(0,z.jsx)(r.A,{id:"homepage.thinking_matters.title",children:"Your Thinking Matters in the Age of AI"})})]}),(0,z.jsx)("div",{style:{cursor:"pointer"},children:(0,z.jsx)("img",{className:d.A.heroImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_benefits.png"),id:"aiflow-overview",alt:"FunBlocks AIFlow interface",src:"/img/portfolio/thumbnails/aiflow_benefits.png"})})]})})}function P(){return(0,z.jsx)("section",{id:"aiflow",className:(0,t.A)(d.A.beyondChatgpt),children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,z.jsx)(r.A,{id:"homepage.beyond.title",children:"Beyond ChatGPT"})}),(0,z.jsx)("p",{className:d.A.sectionDescription,children:(0,z.jsx)(r.A,{id:"homepage.beyond.description",children:"Text chatbox is not the only way to interact with AI. Visualize your thoughts and AI's output in formats that truly enhance your cognitive process."})}),(0,z.jsxs)("div",{className:d.A.benefitsContainer,children:[(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udd2e"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.beyond.visualize.title",children:"Visualize Complex Thinking"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.beyond.visualize.description",children:"Transform linear conversations into multidimensional mind maps that reveal connections, patterns, and insights that text alone cannot convey."})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udca1"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.beyond.accelerate.title",children:"Accelerate Innovative Solutions"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.beyond.accelerate.description",children:"Combine AI's vast knowledge with visual thinking techniques to break through creative barriers and generate breakthrough ideas."})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\u26a1"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.beyond.streamline.title",children:"Streamline Knowledge Work"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.beyond.streamline.description",children:"Transform complex information into structured visual outputs that can be instantly converted into professional deliverables."})})]})]})]})})}function D(e){let{setShowImageSrc:i}=e;return(0,z.jsx)("section",{className:d.A.featureSection,children:(0,z.jsx)("div",{className:"container",children:(0,z.jsxs)("div",{className:d.A.featureGrid,children:[(0,z.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,z.jsx)(c.A,{as:"h2",children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.title",children:"Visualize Complex Thinking"})}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.description",children:"Traditional AI chatbots limit thinking to a linear conversation. FunBlocks AIFlow expands your cognitive horizon with multidimensional mind maps on an infinite canvas."})}),(0,z.jsxs)("ul",{className:d.A.featureList,children:[(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.point1",children:"Explore any topic from multiple angles simultaneously, revealing insights that linear thinking misses"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.point2",children:"Dive infinitely deeper into any subtopic while maintaining the full context, creating comprehensive knowledge structures"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.point3",children:"Let AI generate an entire exploration space with related topics and questions you hadn't considered"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.visualize.point4",children:"Refine your thinking with AI-optimized questions that target the core of complex problems"})})]})]}),(0,z.jsx)("div",{style:{cursor:"pointer",flex:2},children:(0,z.jsx)("img",{className:d.A.featureImage,id:"aiflow_optimize_question",alt:"Mind mapping for deeper thinking",src:"/img/portfolio/thumbnails/aiflow_optimize_question.png",onClick:()=>i("/img/portfolio/fullsize/aiflow_optimize_question.png")})})]})})})}function W(e){let{setShowImageSrc:i}=e;return(0,z.jsx)("section",{className:d.A.featureSection,children:(0,z.jsx)("div",{className:"container",children:(0,z.jsxs)("div",{className:d.A.featureGrid,children:[(0,z.jsx)("div",{style:{cursor:"pointer",flex:2},children:(0,z.jsx)("img",{className:d.A.featureImage,id:"aiflow_productivity",alt:"Creative brainstorming with AI",src:"/img/portfolio/thumbnails/aiflow_productivity.png",onClick:()=>i("/img/portfolio/fullsize/aiflow_productivity.png")})}),(0,z.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,z.jsx)(c.A,{as:"h2",children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.title",children:"Accelerate Innovative Solutions"})}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.description",children:"Move beyond the creative limitations of text-based AI interactions. FunBlocks AIFlow combines AI insights with powerful visual frameworks to spark genuine innovation."})}),(0,z.jsxs)("ul",{className:d.A.featureList,children:[(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.point1",children:"Integrate AI's vast knowledge with visual brainstorming to generate connections human minds might not naturally make"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.point2",children:"Deconstruct complex problems visually to identify root causes and hidden opportunities"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.point3",children:"Apply classic mental models (like SCAMPER, Six Thinking Hats, and First Principles) directly in your mind maps with AI assistance"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.accelerate.point4",children:"Overcome creative blocks by visually expanding idea spaces in multiple directions simultaneously"})})]})]})]})})})}function E(e){let{setShowImageSrc:i}=e;return(0,z.jsx)("section",{className:d.A.featureSection,children:(0,z.jsx)("div",{className:"container",children:(0,z.jsxs)("div",{className:d.A.featureGrid,children:[(0,z.jsxs)("div",{className:d.A.featureContent,style:{flex:2},children:[(0,z.jsx)(c.A,{as:"h2",children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.title",children:"Streamline Knowledge Work"})}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.description",children:"Transform the way you work with information. FunBlocks AIFlow turns complex knowledge work into a visual, collaborative process with immediate, tangible outputs."})}),(0,z.jsxs)("ul",{className:d.A.featureList,children:[(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.point1",children:"Instantly break down overwhelming projects into manageable visual components with AI assistance"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.point2",children:"Interact with AI through an intuitive visual interface that matches how your brain naturally processes complex information"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.point3",children:"Collaborate with AI to explore multiple solution paths simultaneously, identifying the optimal approach faster"})}),(0,z.jsx)("li",{children:(0,z.jsx)(r.A,{id:"homepage.features.streamline.point4",children:"Convert your visual thinking directly into professional deliverables\u2014slides, infographics, documents\u2014with a single click"})})]})]}),(0,z.jsx)("div",{style:{cursor:"pointer",flex:2},children:(0,z.jsx)("img",{className:d.A.featureImage,id:"aiflow_slides_generation",alt:"Productivity enhancement with AIFlow",src:"/img/portfolio/thumbnails/aiflow_slides_generation.png",onClick:()=>i("/img/portfolio/fullsize/aiflow_slides_generation.png")})})]})})})}function O(){return(0,z.jsx)("section",{id:"multi-model",className:d.A.featureSection,style:{backgroundColor:"lightcyan"},children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,z.jsx)(r.A,{id:"homepage.multiModel.title",children:"All Leading AI Models in One Place"})}),(0,z.jsx)("p",{className:d.A.sectionDescription,children:(0,z.jsx)(r.A,{id:"homepage.multiModel.description",children:"Access all mainstream large language models through a single FunBlocks AI subscription. No need to manage multiple subscriptions or switch between platforms."})}),(0,z.jsxs)("div",{className:d.A.multiModelAdvantage,children:[(0,z.jsxs)("div",{className:d.A.modelLogosContainer,children:[(0,z.jsxs)("div",{className:d.A.modelLogoItem,children:[(0,z.jsx)("img",{className:d.A.modelLogo,id:"openai-logo",alt:"OpenAI GPT",src:"/img/models/gpt.png"}),(0,z.jsx)("p",{className:d.A.modelName,children:"OpenAI GPT"})]}),(0,z.jsxs)("div",{className:d.A.modelLogoItem,children:[(0,z.jsx)("img",{className:d.A.modelLogo,id:"claude-logo",alt:"Anthropic Claude",src:"/img/models/claude.png"}),(0,z.jsx)("p",{className:d.A.modelName,children:"Anthropic Claude"})]}),(0,z.jsxs)("div",{className:d.A.modelLogoItem,children:[(0,z.jsx)("img",{className:d.A.modelLogo,id:"gemini-logo",alt:"Google Gemini",src:"/img/models/gemini.png"}),(0,z.jsx)("p",{className:d.A.modelName,children:"Google Gemini"})]}),(0,z.jsxs)("div",{className:d.A.modelLogoItem,children:[(0,z.jsx)("img",{className:d.A.modelLogo,id:"deepseek-logo",alt:"DeepSeek",src:"/img/models/deepseek.png"}),(0,z.jsx)("p",{className:d.A.modelName,children:"DeepSeek"})]})]}),(0,z.jsxs)("div",{className:d.A.benefitsContainer,children:[(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\u2728"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage1.title",children:"One Subscription, All Models"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage1.description",children:"Access all leading AI models through a single FunBlocks subscription. No need to manage multiple accounts or pay separate fees."})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udcb0"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage2.title",children:"Significant Cost Savings"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage2.description",children:"Save up to 70% compared to subscribing to each AI platform separately. One subscription unlocks all premium model capabilities."})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\ude80"}),(0,z.jsx)(c.A,{as:"h4",children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage3.title",children:"Complete Product Access"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.multiModel.advantage3.description",children:"Your subscription gives you full access to all FunBlocks AI products - AIFlow, AI Docs, AI Slides, and more. Only the number of AI requests varies by plan."})})]})]})]}),(0,z.jsx)("div",{className:d.A.modelBenefit,children:(0,z.jsx)("p",{style:{textAlign:"center",fontWeight:"500",marginTop:"2rem"},children:(0,z.jsx)(r.A,{id:"homepage.multiModel.benefit",children:"Experience the power of multiple AI models without the complexity and cost of managing multiple subscriptions."})})})]})})}function H(){return(0,z.jsx)("section",{id:"usecases",className:d.A.useCases,children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,z.jsx)(r.A,{id:"homepage.useCases.title",children:"Use Cases"})}),(0,z.jsx)("p",{className:d.A.sectionDescription,children:(0,z.jsx)(r.A,{id:"homepage.useCases.description",children:"FunBlocks AIFlow adapts to diverse knowledge work scenarios, enhancing your thinking process from exploration to execution."})}),(0,z.jsxs)("div",{className:d.A.useCasesGrid,children:[(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83d\udcda"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.learning.title",children:"Topic-Based Learning"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.learning.description",children:"Build comprehensive knowledge systems and discover cross-disciplinary connections. Transform books, lectures, and resources into interactive visual knowledge maps."})})]}),(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83d\udcca"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.strategy.title",children:"Work Plan Strategy"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.strategy.description",children:"Develop strategic work plans by visually mapping goals, breaking down complex initiatives, and generating solution paths with AI guidance."})})]}),(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83c\udfac"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.analysis.title",children:"Content Analysis"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.analysis.description",children:"Analyze films, literature, and media by mapping narratives, themes, and character relationships. Extract deeper insights and create comprehensive visual breakdowns."})})]}),(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83d\udd0d"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.problem.title",children:"Problem Analysis"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.problem.description",children:"Visualize complex problems from multiple angles, identify root causes, and discover innovative solutions through structured AI-assisted exploration."})})]}),(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83d\udcc4"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.content.title",children:"Content Generation"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.content.description",children:"Transform brainstorming mind maps into polished documents, professional slides, compelling images, and informative infographics with a single click, streamlining your workflow."})})]}),(0,z.jsxs)("div",{className:d.A.useCaseCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.useCaseIcon,children:"\ud83d\udca1"}),(0,z.jsx)("span",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.thinking.title",children:"Thinking Enhancement"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.useCases.thinking.description",children:"Progressively improve your critical and creative thinking abilities through AI-guided mind mapping. Develop advanced cognitive skills through regular use of structured thinking frameworks."})})]})]})]})})}function R(e){let{setShowImageSrc:i}=e;return(0,z.jsx)("section",{id:"workspace",className:(0,t.A)(d.A.workspaceSection),children:(0,z.jsxs)("div",{className:"container",children:[(0,z.jsx)(c.A,{as:"h2",className:d.A.sectionTitle,children:(0,z.jsx)(r.A,{id:"homepage.workspace.title",children:"All-in-one FunBlocks AI Workspace"})}),(0,z.jsx)("p",{className:d.A.sectionDescription,children:(0,z.jsx)(r.A,{id:"homepage.workspace.description",children:"Beyond AIFlow, FunBlocks serves as an all-in-one AI workspace designed to meet all your work and learning requirements, offering seamlessly integrated tools for writing, presenting, brainstorming, and research."})}),(0,z.jsxs)("div",{className:d.A.twoColumnGrid,children:[(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udca1"}),(0,z.jsx)("a",{href:"/aiflow.html",target:"_blank",children:(0,z.jsx)(r.A,{id:"homepage.workspace.aiflow.title",children:"AIFlow"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.workspace.aiflow.description",children:"Unleash creativity with AI-powered whiteboard and mind mapping tools. Visualize complex ideas, brainstorm with AI assistance, and organize your thoughts effectively."})}),(0,z.jsx)("div",{style:{cursor:"pointer"},children:(0,z.jsx)("img",{className:d.A.featureImage,onClick:()=>i("/img/portfolio/fullsize/aiflow_benefits.png"),id:"aiflow-overview",alt:"FunBlocks AIFlow interface",src:"/img/portfolio/thumbnails/aiflow_benefits.png"})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83d\udcdd"}),(0,z.jsx)("a",{href:"/aidocs",target:"_blank",children:(0,z.jsx)(r.A,{id:"homepage.workspace.writer.title",children:"AI Docs"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.workspace.writer.description",children:"Experience Notion-style Block Editor with AI-powered writing assistance. Create beautiful documents, notes, and content with intelligent suggestions and formatting."})}),(0,z.jsx)("div",{style:{cursor:"pointer"},children:(0,z.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/ai_writer_workspace.png",alt:"FunBlocks AI Docs: Notion-style block editor with AI assistant",onClick:()=>i("/img/portfolio/fullsize/ai_writer_workspace.png")})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83c\udf9e\ufe0f"}),(0,z.jsx)("a",{href:"/slides.html",target:"_blank",children:(0,z.jsx)(r.A,{id:"homepage.workspace.slides.title",children:"AI Slides"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.workspace.slides.description",children:"Generate professional presentations with a single click based on any topic. Fully supports Markdown formatting for easy writing and editing of slide content."})}),(0,z.jsx)("div",{style:{cursor:"pointer"},children:(0,z.jsx)("img",{className:d.A.featureImage,src:"/img/portfolio/thumbnails/slides.png",alt:"AI Slides: Effortless slide creation with Markdown, AI, and cloud collaboration",onClick:()=>i("/img/portfolio/fullsize/slides.png")})})]}),(0,z.jsxs)("div",{className:d.A.benefitCard,children:[(0,z.jsxs)("div",{className:d.A.cardTitle,children:[(0,z.jsx)("div",{className:d.A.benefitIcon,children:"\ud83c\udf10"}),(0,z.jsx)("a",{href:"https://chromewebstore.google.com/detail/funblocks-ai-your-ultimat/coodnehmocjfaandkbeknihiagfccoid",target:"_blank",children:(0,z.jsx)(r.A,{id:"homepage.workspace.extension.title",children:"AI Extension"})})]}),(0,z.jsx)("p",{children:(0,z.jsx)(r.A,{id:"homepage.workspace.extension.description",children:"Enhance your browsing experience with an intelligent extension that assists with reading and writing on any webpage. Summarize content, draft responses, and research efficiently."})}),(0,z.jsx)("div",{style:{cursor:"pointer",flex:4},children:(0,z.jsx)("img",{className:d.A.featureImage,id:"aiflow-brainstorming",alt:"FunBlocks AI sidebar assistant interface for enhanced reading and critical thinking",src:"/img/portfolio/thumbnails/ai_reading_en.png",onClick:()=>i("/img/portfolio/fullsize/ai_reading_en.png")})})]})]})]})})}function Q(){const{siteConfig:e,i18n:i}=(0,o.A)(),[s,t]=(0,n.useState)(null);function a(e){window.open().location.href=e}function c(){a(`https://app.${window.location.hostname.includes("funblocks")?window.location.hostname.replace("www.",""):"funblocks.net"}/#/login?source=flow`)}return(0,z.jsxs)(l.A,{title:(0,r.T)({id:"head.title",message:"All-in-One AI Workspace \u2013 From Mind Maps to Slides & Docs"}),description:(0,r.T)({id:"head.description",message:"Explore, think, and create with AI. AI-powered innovative whiteboarding, mind mapping, slide creation, and document tools to boost your creativity and productivity."}),children:[(0,z.jsx)(A.A,{}),(0,z.jsx)(G,{setShowImageSrc:t,toApp:c}),(0,z.jsxs)("main",{children:[(0,z.jsx)(R,{setShowImageSrc:t}),(0,z.jsx)(f.A,{}),(0,z.jsx)(P,{}),(0,z.jsx)(D,{setShowImageSrc:t}),(0,z.jsx)(W,{setShowImageSrc:t}),(0,z.jsx)(E,{setShowImageSrc:t}),(0,z.jsx)(p.A,{}),(0,z.jsx)(O,{}),(0,z.jsx)(w.A,{}),(0,z.jsx)(v.A,{showProductHuntBadges:!0}),(0,z.jsx)(q.A,{}),(0,z.jsx)(L,{setShowImageSrc:t}),(0,z.jsx)(H,{}),(0,z.jsx)(u.A,{avatars:["\ud83d\udc69\u200d\ud83c\udfeb","\ud83d\udc68\u200d\ud83d\udcbc","\ud83d\udc69\u200d\ud83d\udcbc","\ud83d\udc68\u200d\ud83c\udf93","\ud83d\udc68\u200d\ud83c\udfeb","\ud83d\udc69\u200d\ud83c\udf93"],page:"homepage"}),(0,z.jsx)(j.A,{toApp:c,page:"homepage"}),(0,z.jsx)(m.A,{page:"homepage",faqIds:["q0","q01","q1","q2","q3","q4","q5","q6","q7","q8","q9","q10","q11","q12","q14","q15","q16","q17","q18","q19"]})]}),(0,z.jsx)(h.A,{}),s&&(0,z.jsx)(g.A,{imageSrc:s,setImageSrc:t}),(0,z.jsx)(x.A,{page:"homepage"})]})}},63989:(e,i,s)=>{s.d(i,{A:()=>o});s(96540);var n=s(68154),t=s(40797),a=s(74848);const o=function(){const{siteConfig:e}=(0,t.A)(),{title:i,tagline:s,url:o}=e,l={"@context":"https://schema.org","@type":"Organization",name:"FunBlocks AI",url:o,logo:`${o}/img/logo.png`,sameAs:["https://twitter.com/funblocksai","https://www.linkedin.com/company/funblocksai"],description:s};return(0,a.jsxs)(n.m,{children:[(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify(l)}),(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AIFlow",applicationCategory:"ProductivityApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},description:"An AI-driven platform that enhances thinking and productivity through visualization. Convert intricate concepts into engaging visual mind maps, dynamic slides, and informative infographics.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"2030"}})}),(0,a.jsx)("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"What is FunBlocks AIFlow?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AIFlow is an AI-driven platform that enhances thinking and productivity through visualization. It helps you convert intricate concepts into engaging visual mind maps, dynamic slides, and informative infographics."}},{"@type":"Question",name:"Which AI models does FunBlocks support?",acceptedAnswer:{"@type":"Answer",text:"FunBlocks AI supports all mainstream large language models including OpenAI GPT, Anthropic Claude, Google Gemini, and DeepSeek, giving you the freedom to choose the best AI for your specific needs."}},{"@type":"Question",name:"How does FunBlocks AIFlow differ from traditional AI chatbots?",acceptedAnswer:{"@type":"Answer",text:"Unlike traditional AI chatbots that limit thinking to linear conversations, FunBlocks AIFlow expands your cognitive horizon with multidimensional mind maps on an infinite canvas, allowing you to explore topics from multiple angles simultaneously."}}]})})]})}},74648:(e,i,s)=>{s.d(i,{A:()=>A});s(96540);var n=s(34164),t=s(50539);const a="videoSection_rFsi",o="sectionTitle_QAn3",l="sectionDescription_TQMg",r="videoContainer_bfos",c="videoWrapper__9tu",d="videoFrame_FnQH",h="videoFeatures_TNWz",m="featureItem_IV9D",p="featureIcon_ehMe",u="videoCta_SQLf",g="btn_z1hz";var x=s(9303),j=s(56289),f=s(74848);const A=function(e){let{page:i="homepage",videoId:s="tPjuWOjpJIs",titleTranslateId:A=null,descriptionTranslateId:v=null,ctaTranslateId:w=null,ctaUrl:k="https://app.funblocks.net/#/login?source=flow",bg:b="#f0f7ff",customFeatures:I=null}=e;const _=[{icon:"\ud83d\udd0d",title:(0,f.jsx)(t.A,{id:`${i}.video.feature1.title`,children:"Explore"}),description:(0,f.jsx)(t.A,{id:`${i}.video.feature1.description`,children:"See how to explore complex topics visually with AI assistance"})},{icon:"\ud83c\udf1f",title:(0,f.jsx)(t.A,{id:`${i}.video.feature2.title`,children:"Think"}),description:(0,f.jsx)(t.A,{id:`${i}.video.feature2.description`,children:"Learn how to enhance your thinking with visual frameworks"})},{icon:"\u2728",title:(0,f.jsx)(t.A,{id:`${i}.video.feature3.title`,children:"Create"}),description:(0,f.jsx)(t.A,{id:`${i}.video.feature3.description`,children:"Discover how to transform ideas into professional deliverables"})}],y=I||_;return(0,f.jsx)("section",{id:"video-demo",className:a,style:{background:b},children:(0,f.jsxs)("div",{className:"container",children:[(0,f.jsx)(x.A,{as:"h2",className:o,children:(0,f.jsx)(t.A,{id:A||`${i}.video.title`,children:"See FunBlocks AIFlow in Action"})}),(0,f.jsx)("p",{className:l,children:(0,f.jsx)(t.A,{id:v||`${i}.video.description`,children:"Watch how FunBlocks AIFlow transforms the way you think, create, and collaborate"})}),(0,f.jsx)("div",{className:r,children:(0,f.jsx)("div",{className:c,children:(0,f.jsx)("iframe",{className:d,src:`https://www.youtube.com/embed/${s}`,title:"FunBlocks AIFlow Demo",border:"0",style:{border:0},allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})})}),(0,f.jsx)("div",{className:h,children:y.map(((e,i)=>(0,f.jsxs)("div",{className:m,children:[(0,f.jsx)("div",{className:p,children:e.icon}),(0,f.jsx)("h3",{children:e.title}),(0,f.jsx)("p",{children:e.description})]},i)))}),(0,f.jsx)("div",{className:u,children:(0,f.jsx)(j.A,{className:(0,n.A)("button",g),to:"#",onClick:()=>window.open(k,"_blank"),children:(0,f.jsx)(t.A,{id:w||`${i}.video.cta`,children:"Try It Yourself"})})})]})})}},78905:(e,i,s)=>{s.d(i,{A:()=>m});s(96540);var n=s(34164),t=s(50539);const a="btn_4iM2",o="ctaButtons_Cfhe",l="ctaBtn_Hq_p",r="ctaSection_vQl5";var c=s(9303),d=s(56289),h=s(74848);const m=function(e){let{page:i,toUrl:s,toApp:m,customButtonText:p}=e;return(0,h.jsx)("section",{id:"cta",className:r,children:(0,h.jsxs)("div",{className:"container",children:[(0,h.jsx)(c.A,{as:"h2",children:(0,h.jsx)(t.A,{id:`${i}.cta.title`,children:"Ready to Embark on a Knowledge Adventure?"})}),(0,h.jsx)("p",{children:(0,h.jsx)(t.A,{id:`${i}.cta.subtitle`,children:"Join FunBlocks AIFlow and unleash your limitless cognitive potential!"})}),(0,h.jsx)("div",{className:o,children:(0,h.jsx)(d.A,{className:(0,n.A)(a,l),to:s,onClick:s?void 0:()=>m(),children:p||(0,h.jsx)(t.A,{id:"homepage.cta.button",children:"Start Free Trial"})})})]})})}},79912:(e,i,s)=>{s.d(i,{A:()=>c});s(96540);var n=s(50539);const t="modal_osiT",a="modalImage_HWh8",o="close_Y6T6",l="zoomIndicator_r4Py";var r=s(74848);const c=function(e){let{imageSrc:i,setImageSrc:s}=e;const c=()=>{s(null)};return(0,r.jsxs)("div",{className:t,style:{display:"flex"},onClick:c,children:[(0,r.jsx)("span",{className:o,onClick:c,children:"\xd7"}),(0,r.jsx)("img",{className:a,src:i,alt:(0,n.T)({id:"modal.alt",message:"Enlarged view"})}),(0,r.jsx)("div",{className:l,children:(0,r.jsx)(n.A,{id:"modal.click_to_close",children:"Click to close"})})]})}},81896:(e,i,s)=>{s.d(i,{A:()=>t});s(96540);var n=s(74848);const t=function(e){let{page:i}=e;const s=(["aiflow","homepage"].includes(i)?"flow":"slides"===i&&"slides")||"extension_welcome"===i&&"extension",t=`\n    if (typeof window !== 'undefined') {\n      ${`\n    function handleCredentialResponse(response) {\n      window.open('https://app.funblocks.net/#/login?${s?"source="+s+"&":""}g_login_token=' + response.credential, '_blank');\n    }\n  `}\n\n      // \u52a0\u8f7dGoogle Analytics\u811a\u672c\n      const gaScript = document.createElement('script');\n      gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-RYTCZEQK0W';\n      gaScript.async = true;\n      document.head.appendChild(gaScript);\n      \n    window.dataLayer = window.dataLayer || [];\n    function gtag() {\n      window.dataLayer.push(arguments);\n    }\n    gtag('js', new Date());\n    gtag('config', 'G-RYTCZEQK0W');\n  \n\n      // \u52a0\u8f7dGoogle Identity Services\u811a\u672c\n      const gisScript = document.createElement('script');\n      gisScript.src = 'https://accounts.google.com/gsi/client';\n      gisScript.async = true;\n      gisScript.defer = true;\n      document.body.appendChild(gisScript);\n      \n      gisScript.onload = function() {\n        \n    if (typeof window.google !== 'undefined' && window.google.accounts) {\n      window.google.accounts.id.initialize({\n        client_id: '************-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com',\n        callback: handleCredentialResponse\n      });\n      window.google.accounts.id.prompt();\n    }\n  \n      };\n    }\n  `;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:t}})})}},83890:(e,i,s)=>{s.d(i,{A:()=>A});s(96540);var n=s(34164),t=s(50539);const a="btnSecondary_I9vp",o="btnSm_efuA",l="ctaButtons_ngQW",r="sectionTitle_TGU3",c="sectionDescription_wsiT",d="toolsSection__Rt2",h="fullWidthImage_zV7g",m="toolsList_XeMD",p="toolItem_KeCk",u="toolTitle_y9e3",g="toolDescription_GKIq";var x=s(9303),j=s(56289),f=s(74848);const A=function(){return(0,f.jsx)("section",{id:"tools",className:d,children:(0,f.jsxs)("div",{className:"container",children:[(0,f.jsx)(x.A,{as:"h2",className:r,style:{marginBottom:"1rem"},children:(0,f.jsx)(t.A,{id:"homepage.tools.title",children:"Quick Access AI Tools"})}),(0,f.jsx)("p",{className:c,children:(0,f.jsx)(t.A,{id:"homepage.tools.description",children:"FunBlocks AIFlow is a comprehensive creativity and productivity platform with specialized tools designed for specific cognitive challenges."})}),(0,f.jsx)("img",{id:"aitools",alt:"FunBlocks AI Tools",className:h,src:"/img/portfolio/thumbnails/ai_tools.png"}),(0,f.jsxs)("div",{className:m,children:[(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool1.title",children:"AI Mind Map Generator"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool1.description",children:"Easily convert documents, books, and movies into mind maps to help you clarify complex ideas quickly."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool2.title",children:"AI Brainstorming"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool2.description",children:"Combine AI with classic thinking models to brainstorm on specific topics or problems, sparking creativity and innovative solutions."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool3.title",children:"AI Critical Thinking"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool3.description",children:"Enhance your critical thinking skills, gaining insights from multiple perspectives through questioning, analysis, and reflection."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool4.title",children:"AI Slides Generator"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool4.description",children:"Quickly create professional slides and presentations on any topic, making it easy to prepare for your presentations."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool5.title",children:"AI Infographics Generator"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool5.description",children:"Automatically generate infographics and knowledge cards from input text, helping you convey information visually."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool6.title",children:"AI Art Insight"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool6.description",children:"Gain deeper artistic analysis and appreciation by taking photos during your travels and museum visits."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool7.title",children:"AI Education Tool"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool7.description",children:"Break down topics into progressive cognitive levels based on Bloom's educational theories to enhance learning efficiency and teaching effectiveness."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool8.title",children:"AI Psychological Insights"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool8.description",children:"Receive psychological counseling and dream interpretation services anytime, helping you better understand your inner world."})})]}),(0,f.jsxs)("div",{className:p,children:[(0,f.jsx)("div",{className:u,children:(0,f.jsx)("span",{children:(0,f.jsx)(t.A,{id:"homepage.tools.tool9.title",children:"AI Image Generator"})})}),(0,f.jsx)("p",{className:g,children:(0,f.jsx)(t.A,{id:"homepage.tools.tool9.description",children:"Generate personalized avatars and images in your desired style with a single click."})})]})]}),(0,f.jsx)("div",{className:l,children:(0,f.jsx)(j.A,{className:(0,n.A)("button",a,o),to:"https://www.funblocks.net/aitools",target:"_blank",children:(0,f.jsx)(t.A,{id:"homepage.tools.tools_list",children:"AI Tools"})})})]})})}},87263:(e,i,s)=>{s.d(i,{A:()=>x});var n=s(96540),t=s(34164),a=s(50539);const o="sectionTitle_gwu3",l="faqSection_DBlu",r="faqContainer_pGyA",c="faqItem_sov3",d="faqQuestion_LOEA",h="faqArrow_irh3",m="active_RDQl",p="faqAnswer_HbCX";var u=s(74848);function g(e){let{page:i,questionId:s,answerId:o}=e;const[l,r]=(0,n.useState)(!1);return(0,u.jsxs)("div",{className:(0,t.A)(c,{[m]:l}),children:[(0,u.jsxs)("div",{className:d,onClick:()=>{r(!l)},children:[(0,u.jsx)("span",{style:{fontWeight:"normal"},children:(0,u.jsx)(a.A,{id:`${i}.faq.${s}`})}),(0,u.jsx)("div",{className:h,style:{transform:l?"rotate(90deg)":"none"},children:"\u25b6"})]}),(0,u.jsx)("div",{className:p,style:{whiteSpace:"pre-line",display:l?"block":"none"},children:(0,u.jsx)(a.A,{id:`${i}.faq.${o}`})})]})}const x=function(e){let{page:i,faqIds:s}=e;return(0,u.jsx)("section",{id:"faqs",className:(0,t.A)("page-section",l),style:{backgroundColor:"var(--gray)"},children:(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)("h2",{className:o,children:(0,u.jsx)(a.A,{id:`${i}.faq.title`,children:"Frequently Asked Questions"})}),(0,u.jsx)("div",{className:r,children:s.map((e=>(0,u.jsx)(g,{page:i,questionId:e,answerId:`a${e.slice(1)}`},e)))})]})})}}}]);