<!doctype html>
<html lang="zh" dir="ltr" class="plugin-blog plugin-id-default" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">历史博文 | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/blog/archive"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docusaurus_tag" content="default"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="docsearch:docusaurus_tag" content="default"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" property="og:title" content="历史博文 | FunBlocks AI"><meta data-rh="true" name="description" content="历史博文"><meta data-rh="true" property="og:description" content="历史博文"><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/blog/archive"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/blog/archive" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/blog/archive" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/blog/archive" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.2b6e2e3c.css">
<script src="/zh/assets/js/runtime~main.01201d88.js" defer="defer"></script>
<script src="/zh/assets/js/main.36e42d00.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a class="navbar__item navbar__link" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/blog/archive" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/blog/archive" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><header class="hero hero--primary"><div class="container"><h1 class="hero__title">历史博文</h1><p class="hero__subtitle">历史博文</p></div></header><main><section class="margin-vert--lg"><div class="container"><div class="row"><div class="col col--4 margin-vert--lg"><h3 class="anchor anchorWithStickyNavbar_LWe7" id="2025">2025<a href="#2025" class="hash-link" aria-label="2025的直接链接" title="2025的直接链接">​</a></h3><ul><li><a href="/zh/blog/beyond-text-a-visual-revolution-in-ai-interaction">2月18日<!-- --> - <!-- -->Beyond Text - A Visual Revolution in AI Interaction</a></li><li><a href="/zh/blog/Unlocking-Gemini-2-0-Flash-Image-Generation-Potential">3月20日<!-- --> - <!-- -->Unlocking Gemini 2.0 Flash’s Hidden Image Generation Potential</a></li><li><a href="/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world">3月29日<!-- --> - <!-- -->When the Internet Meets Ghibli - How AIFlow Turns Your Photos into an Animated World</a></li></ul></div><div class="col col--4 margin-vert--lg"><h3 class="anchor anchorWithStickyNavbar_LWe7" id="2024">2024<a href="#2024" class="hash-link" aria-label="2024的直接链接" title="2024的直接链接">​</a></h3><ul><li><a href="/zh/blog/what-if-notion-ai-was-available-everywhere">3月9日<!-- --> - <!-- -->What if Notion AI Was Available Everywhere?</a></li><li><a href="/zh/blog/mindmap-llm-the-future-of-ai-interaction">6月18日<!-- --> - <!-- -->Mindmap + LLM = The Future of AI Interaction?</a></li><li><a href="/zh/blog/what-if-asking-a-question-could-unlock-a-universe-of-knowledge">6月21日<!-- --> - <!-- -->What If Asking a Question Could Unlock a Universe of Knowledge?</a></li><li><a href="/zh/blog/beyond-chatgpt-how-llms-power-dynamic-ui-for-seamless-user-experience">6月27日<!-- --> - <!-- -->How LLMs Power Dynamic UI for Seamless User Experience</a></li><li><a href="/zh/blog/funblocks-aiflow-on-product-hunt-a-start-not-an-end">9月14日<!-- --> - <!-- -->FunBlocks AIFlow on Product Hunt</a></li><li><a href="/zh/blog/i-developed-an-ai-infographic-generator-with-cursor-in-just-one-week">10月25日<!-- --> - <!-- -->I Developed an AI Infographic Generator with Cursor in Just One Week</a></li><li><a href="/zh/blog/how-i-used-funblocks-ai-to-launch-successfully-on-product-hunt-a-real-world-marketing-case-study">11月4日<!-- --> - <!-- -->How I Used FunBlocks AI to Launch Successfully on Product Hunt</a></li><li><a href="/zh/blog/how-to-leverage-ai-for-marketing-success-funblocks-aiflow-explained">11月7日<!-- --> - <!-- -->How to Leverage AI for Marketing Success – FunBlocks AIFlow Explained</a></li></ul></div></div></div></section></main></div></div>
</body>
</html>