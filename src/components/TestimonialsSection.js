import React from 'react';
import clsx from 'clsx';
import Translate from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from './TestimonialsSection.module.css';

export default function TestimonialsSection({ avatars, page }) {
  const testimonials = [
    {
      avatar: avatars[0],
      name: <Translate id={`${page}.testimonials.user1.name`}><PERSON></Translate>,
      role: <Translate id={`${page}.testimonials.user1.role`}>Graduate Student</Translate>,
      content: <Translate id={`${page}.testimonials.user1.content`}>
        "This extension is a game-changer for research! I use it to create mind maps from academic papers and lecture videos. The AI's ability to extract key concepts is impressive."
      </Translate>
    },
    {
      avatar: avatars[1],
      name: <Translate id={`${page}.testimonials.user2.name`}><PERSON></Translate>,
      role: <Translate id={`${page}.testimonials.user2.role`}>Content Creator</Translate>,
      content: <Translate id={`${page}.testimonials.user2.content`}>
        "FunBlocks AI Mindmap has transformed how I organize content ideas. The instant mind maps from web research save me hours of work every week!"
      </Translate>
    },
    {
      avatar: avatars[2],
      name: <Translate id={`${page}.testimonials.user3.name`}>Prof. Michael Brown</Translate>,
      role: <Translate id={`${page}.testimonials.user3.role`}>University Lecturer</Translate>,
      content: <Translate id={`${page}.testimonials.user3.content`}>
        "I recommend this tool to all my students. It helps them visualize complex topics and improves their understanding of course materials."
      </Translate>
    },
    {
      avatar: avatars[3],
      name: <Translate id={`${page}.testimonials.user4.name`}>Emma Zhang</Translate>,
      role: <Translate id={`${page}.testimonials.user4.role`}>High School Student</Translate>,
      content: <Translate id={`${page}.testimonials.user4.content`}>
        "Love how it turns YouTube educational videos into mind maps! Makes studying so much easier and more fun."
      </Translate>
    },
    {
      avatar: avatars[4],
      name: <Translate id={`${page}.testimonials.user5.name`}>James Wilson</Translate>,
      role: <Translate id={`${page}.testimonials.user5.role`}>Business Analyst</Translate>,
      content: <Translate id={`${page}.testimonials.user5.content`}>
        "Perfect for analyzing market research and competitor websites. The integration with AIFlow makes it a powerful tool for business intelligence."
      </Translate>
    },
    {
      avatar: avatars[5],
      name: <Translate id={`${page}.testimonials.user6.name`}>Dr. Lisa Wang</Translate>,
      role: <Translate id={`${page}.testimonials.user6.role`}>Research Scientist</Translate>,
      content: <Translate id={`${page}.testimonials.user6.content`}>
        "The AI's ability to identify connections between concepts is remarkable. It's become an essential tool for my literature reviews."
      </Translate>
    }
  ];

  return (
    <section className={styles.testimonialsSection}>
      <div className="container">
        <div className={styles.sectionHeading}>
          <Heading as="h2" className={styles.sectionTitle}>
            <Translate id={`${page}.testimonials.title`}>
              What Our Users Say
            </Translate>
          </Heading>
          <p className={styles.sectionDescription}>
            <Translate id={`${page}.testimonials.description`}>
              Join thousands of satisfied users who are transforming how they learn and work
            </Translate>
          </p>
        </div>

        <div className={styles.testimonialGrid}>
          {testimonials.map((testimonial, index) => (
            <div key={index} className={styles.testimonialCard}>
              <div className={styles.testimonialHeader}>
                <span className={styles.testimonialAvatar}>{testimonial.avatar}</span>
                <div className={styles.testimonialMeta}>
                  <h3 className={styles.testimonialName}>{testimonial.name}</h3>
                  <p className={styles.testimonialRole}>{testimonial.role}</p>
                </div>
              </div>
              <blockquote className={styles.testimonialContent}>
                {testimonial.content}
              </blockquote>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
