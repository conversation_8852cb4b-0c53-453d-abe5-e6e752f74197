.testimonialsSection {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.sectionHeading {
  text-align: center;
  margin-bottom: 60px;
}

.sectionTitle {
  font-size: 2.5em;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.sectionDescription {
  font-size: 1.2em;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.testimonialGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.testimonialCard {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.testimonialCard:hover {
  transform: translateY(-5px);
}

.testimonialHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.testimonialAvatar {
  font-size: 2em;
  margin-right: 12px;
}

.testimonialMeta {
  flex: 1;
}

.testimonialName {
  font-size: 1.1em;
  font-weight: bold;
  margin: 0;
  color: #1a1a1a;
}

.testimonialRole {
  font-size: 0.9em;
  color: #666;
  margin: 4px 0 0;
}

.testimonialContent {
  margin: 0;
  color: #444;
  font-size: 1em;
  line-height: 1.6;
}
