/* AI Mindmap Extension Page Styles */

:root {
  --mindmap-primary: #667eea;
  --mindmap-secondary: #764ba2;
  --mindmap-accent: #ff6b6b;
  --mindmap-dark: #333333;
  --mindmap-light: #ffffff;
  --mindmap-gray: #f5f7fa;
  --mindmap-text: #333333;
  --mindmap-text-light: #555555;
}

/* Hero Section */
.hero {
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
  color: white;
}

.heroRow {
  display: flex;
  align-items: center;
  gap: 3rem;
  min-height: 500px;
}

.heroContent {
  z-index: 10;
  position: relative;
}

.heroBadge {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.hero h1 {
  font-size: 3.2rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: white;
}

.heroSubtitle {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.btnPrimary {
  background-color: white;
  color: var(--mindmap-primary);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.btnPrimary:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--mindmap-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btnSecondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.btnSecondary:hover {
  background-color: white;
  color: var(--mindmap-primary);
}

.heroStats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.heroStat {
  text-align: center;
}

.heroStatNumber {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
}

.heroStatLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.heroImageContainer {
  position: relative;
}

.heroImageWrapper {
  flex: 1;
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.heroImage {
  width: 100%;
  height: auto;
  cursor: pointer;
  transition: transform 0.3s ease;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.heroImage:hover {
  transform: scale(1.02);
}

.heroImageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.heroImageWrapper:hover .heroImageOverlay {
  opacity: 1;
}

.heroImageOverlayText {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Feature Sections */
.featureSection {
  padding: 6rem 0;
  background-color: var(--mindmap-gray);
}

.featureSection:nth-child(even) {
  background-color: var(--mindmap-light);
}

.sectionHeading {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  color: var(--mindmap-dark);
}

.sectionDescription {
  font-size: 1.2rem;
  color: var(--mindmap-text-light);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Workflow Steps */
.workflowSteps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
  margin-top: 4rem;
}

.workflowStep {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.workflowStep:hover {
  transform: translateY(-5px);
}

.stepNumber {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--mindmap-primary), var(--mindmap-secondary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 1.5rem;
}

.stepTitle {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--mindmap-dark);
}

.stepDescription {
  color: var(--mindmap-text-light);
  line-height: 1.5;
}

/* Feature Grid */
.featureGrid {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin-top: 4rem;
}

.featureContent {
  flex: 1;
}

.featureBadge {
  display: inline-block;
  background-color: var(--mindmap-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.featureTitle {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--mindmap-dark);
  font-weight: 700;
}

.featureDescription {
  font-size: 1.1rem;
  color: var(--mindmap-text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featureList {
  list-style: none;
  padding: 0;
}

.featureList li {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--mindmap-text-light);
}

.featureIcon {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.featureImageWrapper {
  flex: 1;
}

.featureImageContainer {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.featureImageContainer:hover {
  transform: translateY(-5px);
}

.featureImage {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.featureImageContainer:hover .featureImage {
  transform: scale(1.02);
}

.featureImageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featureImageContainer:hover .featureImageOverlay {
  opacity: 1;
}

.featureImageOverlayText {
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

/* Page Section */
.pageSection {
  position: relative;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .heroRow {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .hero h1 {
    font-size: 2.2rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .heroButtons {
    flex-direction: column;
    align-items: center;
  }

  .heroStats {
    justify-content: center;
    gap: 1.5rem;
  }

  .workflowSteps {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .featureGrid {
    flex-direction: column;
    gap: 2rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionDescription {
    font-size: 1rem;
  }
}
